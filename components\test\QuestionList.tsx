'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Edit, 
  Trash2, 
  GripVertical,
  Clock,
  Target,
  Tag,
  Volume2,
  Image,
  FileText,
  CheckCircle,
  Circle,
  Type,
  PenTool
} from 'lucide-react'

interface Question {
  id: string
  type: string
  content: string
  order: number
  points: number
  difficulty: string
  tags: string[]
  options?: any
  correctAnswer?: any
  explanation?: string
  audioUrl?: string
  imageUrl?: string
  estimatedTime?: number
}

interface TestSection {
  id: string
  title: string
  description: string
  order: number
  duration?: number
  questions: Question[]
}

interface QuestionListProps {
  section: TestSection
  onEditQuestion: (question: Question) => void
  onDeleteQuestion: (questionId: string) => void
  onAddQuestion: () => void
}

export function QuestionList({ 
  section, 
  onEditQuestion, 
  onDeleteQuestion, 
  onAddQuestion 
}: QuestionListProps) {
  const [draggedQuestion, setDraggedQuestion] = useState<string | null>(null)

  const getQuestionTypeIcon = (type: string) => {
    switch (type) {
      case 'MULTIPLE_CHOICE':
        return <CheckCircle className="h-4 w-4" />
      case 'TRUE_FALSE':
        return <Circle className="h-4 w-4" />
      case 'FILL_IN_BLANK':
        return <Type className="h-4 w-4" />
      case 'ESSAY':
        return <PenTool className="h-4 w-4" />
      case 'LISTENING_COMPREHENSION':
        return <Volume2 className="h-4 w-4" />
      case 'READING_COMPREHENSION':
        return <FileText className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getQuestionTypeColor = (type: string) => {
    switch (type) {
      case 'MULTIPLE_CHOICE':
        return 'bg-blue-100 text-blue-800'
      case 'TRUE_FALSE':
        return 'bg-green-100 text-green-800'
      case 'FILL_IN_BLANK':
        return 'bg-yellow-100 text-yellow-800'
      case 'ESSAY':
        return 'bg-purple-100 text-purple-800'
      case 'LISTENING_COMPREHENSION':
        return 'bg-orange-100 text-orange-800'
      case 'READING_COMPREHENSION':
        return 'bg-indigo-100 text-indigo-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'bg-green-100 text-green-800'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800'
      case 'HARD':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatQuestionType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const getQuestionPreview = (question: Question) => {
    const maxLength = 100
    let preview = question.content

    if (preview.length > maxLength) {
      preview = preview.substring(0, maxLength) + '...'
    }

    return preview
  }

  const sortedQuestions = [...section.questions].sort((a, b) => a.order - b.order)

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{section.title}</CardTitle>
            <CardDescription>
              {section.description || 'No description provided'}
            </CardDescription>
          </div>
          <Button onClick={onAddQuestion}>
            <Plus className="mr-2 h-4 w-4" />
            Add Question
          </Button>
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>{section.questions.length} questions</span>
          {section.duration && (
            <span className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              {section.duration} min
            </span>
          )}
          <span className="flex items-center gap-1">
            <Target className="h-4 w-4" />
            {section.questions.reduce((sum, q) => sum + q.points, 0)} points total
          </span>
        </div>
      </CardHeader>
      <CardContent>
        {sortedQuestions.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No questions yet</h3>
            <p className="text-muted-foreground mb-4">
              Start building your test by adding questions to this section.
            </p>
            <Button onClick={onAddQuestion}>
              <Plus className="mr-2 h-4 w-4" />
              Add First Question
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {sortedQuestions.map((question, index) => (
              <div
                key={question.id}
                className="border rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start gap-4">
                  {/* Drag Handle */}
                  <div className="flex items-center justify-center w-8 h-8 text-muted-foreground cursor-grab">
                    <GripVertical className="h-4 w-4" />
                  </div>

                  {/* Question Number */}
                  <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full text-sm font-medium">
                    {index + 1}
                  </div>

                  {/* Question Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getQuestionTypeIcon(question.type)}
                        <Badge className={getQuestionTypeColor(question.type)}>
                          {formatQuestionType(question.type)}
                        </Badge>
                        <Badge className={getDifficultyColor(question.difficulty)}>
                          {question.difficulty}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {question.points} {question.points === 1 ? 'point' : 'points'}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        {question.audioUrl && (
                          <Volume2 className="h-4 w-4 text-orange-600" />
                        )}
                        {question.imageUrl && (
                          <Image className="h-4 w-4 text-blue-600" />
                        )}
                        {question.estimatedTime && (
                          <span className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {question.estimatedTime}s
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm font-medium mb-1">
                        {getQuestionPreview(question)}
                      </p>
                      
                      {/* Question Type Specific Preview */}
                      {question.type === 'MULTIPLE_CHOICE' && question.options?.choices && (
                        <div className="text-xs text-muted-foreground">
                          {question.options.choices.length} options
                        </div>
                      )}
                      
                      {question.type === 'FILL_IN_BLANK' && (
                        <div className="text-xs text-muted-foreground">
                          Fill-in-the-blank question
                        </div>
                      )}
                      
                      {question.type === 'ESSAY' && (
                        <div className="text-xs text-muted-foreground">
                          Essay question
                          {question.options?.minWords && ` • Min: ${question.options.minWords} words`}
                          {question.options?.maxWords && ` • Max: ${question.options.maxWords} words`}
                        </div>
                      )}
                    </div>

                    {/* Tags */}
                    {question.tags.length > 0 && (
                      <div className="flex items-center gap-1 mb-2">
                        <Tag className="h-3 w-3 text-muted-foreground" />
                        <div className="flex flex-wrap gap-1">
                          {question.tags.map((tag, tagIndex) => (
                            <Badge key={tagIndex} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Explanation Preview */}
                    {question.explanation && (
                      <div className="text-xs text-muted-foreground">
                        <span className="font-medium">Explanation:</span> {
                          question.explanation.length > 50 
                            ? question.explanation.substring(0, 50) + '...'
                            : question.explanation
                        }
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEditQuestion(question)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDeleteQuestion(question.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

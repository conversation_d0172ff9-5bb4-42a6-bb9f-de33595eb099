import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, validatePagination, handleApiError } from '@/lib/middleware'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30' // days
    const organizationId = searchParams.get('organizationId')

    // Calculate date range
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(timeRange))

    // Build where clause based on user role
    const whereClause: any = {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    }

    // Role-based filtering
    if (user.role === 'TEACHER') {
      whereClause.OR = [
        { creatorId: user.id },
        {
          test: {
            creatorId: user.id
          }
        },
        {
          user: {
            classStudents: {
              some: {
                class: {
                  teacherId: user.id
                }
              }
            }
          }
        }
      ]
    } else if (user.role === 'STUDENT') {
      whereClause.userId = user.id
    } else if (organizationId && user.role === 'ADMIN') {
      whereClause.user = {
        organizationId
      }
    } else if (user.organizationId) {
      whereClause.user = {
        organizationId: user.organizationId
      }
    }

    // Get comprehensive analytics data
    const [
      totalTests,
      totalResults,
      totalUsers,
      totalClasses,
      recentResults,
      testTypeDistribution,
      performanceByLevel,
      dailyActivity,
      topPerformers,
      averageScores
    ] = await Promise.all([
      // Total tests created
      db.test.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          ...(user.role === 'TEACHER' && { creatorId: user.id }),
          ...(user.organizationId && user.role !== 'STUDENT' && {
            organizationId: user.organizationId
          })
        }
      }),

      // Total test results
      db.testResult.count({
        where: whereClause
      }),

      // Total active users
      db.user.count({
        where: {
          lastLogin: {
            gte: startDate
          },
          ...(user.organizationId && user.role !== 'STUDENT' && {
            organizationId: user.organizationId
          })
        }
      }),

      // Total classes
      db.class.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          ...(user.role === 'TEACHER' && { teacherId: user.id }),
          ...(user.organizationId && user.role !== 'STUDENT' && {
            organizationId: user.organizationId
          })
        }
      }),

      // Recent test results
      db.testResult.findMany({
        where: whereClause,
        include: {
          test: {
            select: {
              title: true,
              type: true
            }
          },
          user: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: {
          submittedAt: 'desc'
        },
        take: 10
      }),

      // Test type distribution
      db.testResult.groupBy({
        by: ['testId'],
        where: whereClause,
        _count: {
          id: true
        },
        _avg: {
          percentage: true
        }
      }),

      // Performance by English level
      db.testResult.findMany({
        where: whereClause,
        include: {
          test: {
            select: {
              level: true
            }
          }
        }
      }),

      // Daily activity for the time range
      db.testResult.findMany({
        where: whereClause,
        select: {
          submittedAt: true,
          score: true,
          percentage: true
        }
      }),

      // Top performers
      db.testResult.groupBy({
        by: ['userId'],
        where: whereClause,
        _avg: {
          percentage: true,
          score: true
        },
        _count: {
          id: true
        },
        orderBy: {
          _avg: {
            percentage: 'desc'
          }
        },
        take: 10
      }),

      // Average scores by test type
      db.testResult.findMany({
        where: whereClause,
        include: {
          test: {
            select: {
              type: true
            }
          }
        }
      })
    ])

    // Process test type distribution
    const testTypes = await db.test.findMany({
      where: {
        id: {
          in: testTypeDistribution.map(t => t.testId)
        }
      },
      select: {
        id: true,
        type: true,
        title: true
      }
    })

    const typeDistribution = testTypes.reduce((acc, test) => {
      const result = testTypeDistribution.find(t => t.testId === test.id)
      if (result) {
        if (!acc[test.type]) {
          acc[test.type] = {
            count: 0,
            averageScore: 0,
            totalScore: 0
          }
        }
        acc[test.type].count += result._count.id
        acc[test.type].totalScore += (result._avg.percentage || 0) * result._count.id
      }
      return acc
    }, {} as Record<string, { count: number; averageScore: number; totalScore: number }>)

    // Calculate average scores for each type
    Object.keys(typeDistribution).forEach(type => {
      const data = typeDistribution[type]
      data.averageScore = data.count > 0 ? data.totalScore / data.count : 0
    })

    // Process performance by level
    const levelPerformance = performanceByLevel.reduce((acc, result) => {
      const level = result.test.level || 'Unknown'
      if (!acc[level]) {
        acc[level] = {
          count: 0,
          totalScore: 0,
          averageScore: 0,
          passCount: 0
        }
      }
      acc[level].count++
      acc[level].totalScore += result.percentage
      if (result.passed) acc[level].passCount++
      return acc
    }, {} as Record<string, { count: number; totalScore: number; averageScore: number; passCount: number }>)

    Object.keys(levelPerformance).forEach(level => {
      const data = levelPerformance[level]
      data.averageScore = data.count > 0 ? data.totalScore / data.count : 0
    })

    // Process daily activity
    const dailyStats = dailyActivity.reduce((acc, result) => {
      const date = result.submittedAt.toISOString().split('T')[0]
      if (!acc[date]) {
        acc[date] = {
          date,
          testsCompleted: 0,
          totalScore: 0,
          averageScore: 0
        }
      }
      acc[date].testsCompleted++
      acc[date].totalScore += result.percentage
      return acc
    }, {} as Record<string, { date: string; testsCompleted: number; totalScore: number; averageScore: number }>)

    Object.keys(dailyStats).forEach(date => {
      const data = dailyStats[date]
      data.averageScore = data.testsCompleted > 0 ? data.totalScore / data.testsCompleted : 0
    })

    // Get user details for top performers
    const topPerformerUsers = await db.user.findMany({
      where: {
        id: {
          in: topPerformers.map(p => p.userId)
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true
      }
    })

    const topPerformersWithDetails = topPerformers.map(performer => {
      const userDetails = topPerformerUsers.find(u => u.id === performer.userId)
      return {
        ...performer,
        user: userDetails
      }
    })

    // Calculate overall statistics
    const overallStats = {
      totalTests,
      totalResults,
      totalUsers,
      totalClasses,
      averageScore: totalResults > 0 
        ? Math.round(recentResults.reduce((sum, r) => sum + r.percentage, 0) / recentResults.length)
        : 0,
      passRate: totalResults > 0
        ? Math.round((recentResults.filter(r => r.passed).length / recentResults.length) * 100)
        : 0,
      averageTimeSpent: totalResults > 0
        ? Math.round(recentResults.reduce((sum, r) => sum + r.timeSpent, 0) / recentResults.length)
        : 0
    }

    return NextResponse.json({
      timeRange: parseInt(timeRange),
      overallStats,
      typeDistribution,
      levelPerformance,
      dailyActivity: Object.values(dailyStats).sort((a, b) => a.date.localeCompare(b.date)),
      topPerformers: topPerformersWithDetails,
      recentResults: recentResults.map(result => ({
        id: result.id,
        testTitle: result.test.title,
        testType: result.test.type,
        userName: result.user.name,
        userEmail: result.user.email,
        score: result.score,
        percentage: result.percentage,
        passed: result.passed,
        submittedAt: result.submittedAt
      }))
    })

  } catch (error) {
    return handleApiError(error, 'GET /api/analytics')
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers and admins can create questions
    if (session.user.role === 'STUDENT') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const {
      sectionId,
      type,
      content,
      points,
      difficulty,
      tags,
      options,
      correctAnswer,
      explanation,
      audioUrl,
      imageUrl,
      estimatedTime
    } = body

    // Validation
    if (!sectionId || !type || !content) {
      return NextResponse.json(
        { error: 'Section ID, type, and content are required' },
        { status: 400 }
      )
    }

    // Verify the section exists and user has access
    const section = await db.testSection.findUnique({
      where: { id: sectionId },
      include: {
        test: {
          include: {
            creator: {
              select: { id: true }
            }
          }
        }
      }
    })

    if (!section) {
      return NextResponse.json({ error: 'Section not found' }, { status: 404 })
    }

    // Check permissions
    const canEdit = 
      session.user.role === 'ADMIN' ||
      section.test.creator.id === session.user.id

    if (!canEdit) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Get the next order number
    const lastQuestion = await db.question.findFirst({
      where: { sectionId },
      orderBy: { order: 'desc' }
    })
    const nextOrder = (lastQuestion?.order || 0) + 1

    // Create the question
    const question = await db.question.create({
      data: {
        sectionId,
        type,
        content,
        order: nextOrder,
        points: points || 1.0,
        difficulty: difficulty || 'MEDIUM',
        tags: tags || [],
        options: options || {},
        correctAnswer: correctAnswer || {},
        explanation,
        audioUrl,
        imageUrl,
        estimatedTime
      }
    })

    return NextResponse.json({ question }, { status: 201 })
  } catch (error) {
    console.error('Failed to create question:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const sectionId = searchParams.get('sectionId')
    const testId = searchParams.get('testId')

    if (!sectionId && !testId) {
      return NextResponse.json(
        { error: 'Section ID or Test ID is required' },
        { status: 400 }
      )
    }

    let where: any = {}

    if (sectionId) {
      where.sectionId = sectionId
    } else if (testId) {
      where.section = {
        testId
      }
    }

    const questions = await db.question.findMany({
      where,
      include: {
        section: {
          include: {
            test: {
              include: {
                creator: {
                  select: { id: true, name: true }
                }
              }
            }
          }
        }
      },
      orderBy: [
        { section: { order: 'asc' } },
        { order: 'asc' }
      ]
    })

    // Check access permissions for each question
    const accessibleQuestions = questions.filter(question => {
      const test = question.section.test
      return (
        test.isPublic ||
        session.user.role === 'ADMIN' ||
        test.creator.id === session.user.id
      )
    })

    return NextResponse.json({ questions: accessibleQuestions })
  } catch (error) {
    console.error('Failed to fetch questions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

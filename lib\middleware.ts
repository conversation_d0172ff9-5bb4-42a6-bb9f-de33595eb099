import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { UserRole } from '@prisma/client'
import { NextRequest, NextResponse } from 'next/server'

export interface AuthenticatedRequest extends NextRequest {
  user: {
    id: string
    email: string
    name: string
    role: UserRole
    organizationId?: string
  }
}

/**
 * Middleware to check if user is authenticated
 */
export async function requireAuth(request: NextRequest): Promise<{
  user: any
  error?: NextResponse
}> {
  const session = await getServerSession(authOptions)
  
  if (!session) {
    return {
      user: null,
      error: NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  }

  return { user: session.user }
}

/**
 * Middleware to check if user has required role
 */
export async function requireRole(
  request: NextRequest, 
  allowedRoles: UserRole[]
): Promise<{
  user: any
  error?: NextResponse
}> {
  const { user, error } = await requireAuth(request)
  
  if (error) {
    return { user: null, error }
  }

  if (!allowedRoles.includes(user.role)) {
    return {
      user: null,
      error: NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }
  }

  return { user }
}

/**
 * Middleware to check if user is admin
 */
export async function requireAdmin(request: NextRequest) {
  return requireRole(request, [UserRole.ADMIN])
}

/**
 * Middleware to check if user is teacher or admin
 */
export async function requireTeacher(request: NextRequest) {
  return requireRole(request, [UserRole.TEACHER, UserRole.ADMIN])
}

/**
 * Middleware to check if user is student, teacher, or admin
 */
export async function requireStudent(request: NextRequest) {
  return requireRole(request, [UserRole.STUDENT, UserRole.TEACHER, UserRole.ADMIN])
}

/**
 * Check if user owns a resource or has admin privileges
 */
export function canAccessResource(
  userRole: UserRole,
  userId: string,
  resourceOwnerId: string,
  organizationId?: string,
  userOrganizationId?: string
): boolean {
  // Admin can access everything
  if (userRole === UserRole.ADMIN) {
    return true
  }

  // User can access their own resources
  if (userId === resourceOwnerId) {
    return true
  }

  // Teachers can access resources in their organization
  if (userRole === UserRole.TEACHER && organizationId && userOrganizationId) {
    return organizationId === userOrganizationId
  }

  return false
}

/**
 * Validate pagination parameters
 */
export function validatePagination(searchParams: URLSearchParams): {
  page: number
  limit: number
  skip: number
} {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10')))
  const skip = (page - 1) * limit

  return { page, limit, skip }
}

/**
 * Validate and sanitize search query
 */
export function validateSearch(searchParams: URLSearchParams): string | null {
  const search = searchParams.get('search')
  if (!search || search.trim().length === 0) {
    return null
  }
  
  // Basic sanitization - remove special characters that could cause issues
  return search.trim().replace(/[<>]/g, '')
}

/**
 * Create standardized API response
 */
export function createApiResponse<T>(
  data: T,
  status: number = 200,
  message?: string
): NextResponse {
  const response: any = { data }
  
  if (message) {
    response.message = message
  }

  return NextResponse.json(response, { status })
}

/**
 * Create standardized error response
 */
export function createErrorResponse(
  error: string,
  status: number = 400,
  details?: any
): NextResponse {
  const response: any = { error }
  
  if (details) {
    response.details = details
  }

  return NextResponse.json(response, { status })
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error: any, context: string): NextResponse {
  console.error(`API Error in ${context}:`, error)
  
  // Database constraint errors
  if (error.code === 'P2002') {
    return createErrorResponse('Resource already exists', 409)
  }
  
  // Foreign key constraint errors
  if (error.code === 'P2003') {
    return createErrorResponse('Referenced resource not found', 400)
  }
  
  // Record not found errors
  if (error.code === 'P2025') {
    return createErrorResponse('Resource not found', 404)
  }

  // Validation errors
  if (error.name === 'ValidationError') {
    return createErrorResponse('Validation failed', 400, error.errors)
  }

  // Default server error
  return createErrorResponse('Internal server error', 500)
}

/**
 * Validate required fields in request body
 */
export function validateRequiredFields(
  body: any,
  requiredFields: string[]
): string[] {
  const missingFields: string[] = []
  
  for (const field of requiredFields) {
    if (body[field] === undefined || body[field] === null || body[field] === '') {
      missingFields.push(field)
    }
  }
  
  return missingFields
}

/**
 * Rate limiting helper (basic implementation)
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 60000 // 1 minute
): boolean {
  const now = Date.now()
  const record = rateLimitMap.get(identifier)
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(identifier, {
      count: 1,
      resetTime: now + windowMs
    })
    return true
  }
  
  if (record.count >= maxRequests) {
    return false
  }
  
  record.count++
  return true
}

/**
 * Clean up expired rate limit records
 */
export function cleanupRateLimit() {
  const now = Date.now()
  for (const [key, record] of rateLimitMap.entries()) {
    if (now > record.resetTime) {
      rateLimitMap.delete(key)
    }
  }
}

// Clean up rate limit records every 5 minutes
setInterval(cleanupRateLimit, 5 * 60 * 1000)

import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, requireAdmin, validatePagination, validateSearch, handleApi<PERSON>rror, validateRequiredFields } from '@/lib/middleware'
import { db } from '@/lib/db'
import { hashPassword } from '@/lib/auth'
import { UserRole, OrganizationType } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = validatePagination(searchParams)
    const search = validateSearch(searchParams)
    const role = searchParams.get('role') as UserRole | null
    const organizationId = searchParams.get('organizationId')

    // Build where clause based on user role and filters
    const where: any = {}

    // Role-based filtering
    if (user.role === 'TEACHER') {
      // Teachers can only see users in their organization
      where.organizationId = user.organizationId
    } else if (user.role === 'STUDENT') {
      // Students can only see themselves and their classmates
      where.OR = [
        { id: user.id },
        {
          classStudents: {
            some: {
              class: {
                students: {
                  some: {
                    studentId: user.id
                  }
                }
              }
            }
          }
        }
      ]
    }
    // Admins can see all users (no additional filtering)

    // Apply filters
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ]
    }
    if (role) {
      where.role = role
    }
    if (organizationId && (user.role === 'ADMIN' || user.organizationId === organizationId)) {
      where.organizationId = organizationId
    }

    const [users, total] = await Promise.all([
      db.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          createdAt: true,
          lastLogin: true,
          organization: {
            select: {
              id: true,
              name: true,
              type: true
            }
          },
          profile: {
            select: {
              firstName: true,
              lastName: true,
              currentLevel: true,
              targetLevel: true
            }
          },
          _count: {
            select: {
              createdTests: true,
              testResults: true,
              classStudents: true,
              teacherClasses: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      db.user.count({ where })
    ])

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    return handleApiError(error, 'GET /api/users')
  }
}

export async function POST(request: NextRequest) {
  try {
    const { user, error } = await requireAdmin(request)
    if (error) return error

    const body = await request.json()
    const requiredFields = ['email', 'name', 'password', 'role']
    const missingFields = validateRequiredFields(body, requiredFields)

    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      )
    }

    const {
      email,
      name,
      password,
      role,
      organizationId,
      profile
    } = body

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate role
    if (!Object.values(UserRole).includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingUser = await db.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      )
    }

    // Validate organization if provided
    if (organizationId) {
      const organization = await db.organization.findUnique({
        where: { id: organizationId }
      })

      if (!organization) {
        return NextResponse.json(
          { error: 'Organization not found' },
          { status: 400 }
        )
      }
    }

    // Hash password
    const hashedPassword = await hashPassword(password)

    // Create user with profile
    const newUser = await db.user.create({
      data: {
        email,
        name,
        password: hashedPassword,
        role,
        organizationId,
        profile: profile ? {
          create: {
            firstName: profile.firstName,
            lastName: profile.lastName,
            dateOfBirth: profile.dateOfBirth ? new Date(profile.dateOfBirth) : null,
            phoneNumber: profile.phoneNumber,
            address: profile.address,
            city: profile.city,
            country: profile.country,
            currentLevel: profile.currentLevel,
            targetLevel: profile.targetLevel,
            ieltsTarget: profile.ieltsTarget,
            previousIELTS: profile.previousIELTS,
            ieltsTestDate: profile.ieltsTestDate ? new Date(profile.ieltsTestDate) : null,
            preferredLanguage: profile.preferredLanguage || 'en',
            timezone: profile.timezone || 'UTC',
            notifications: profile.notifications !== false
          }
        } : undefined
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        profile: true
      }
    })

    // Remove password from response
    const { password: _, ...userResponse } = newUser

    return NextResponse.json({ user: userResponse }, { status: 201 })
  } catch (error) {
    return handleApiError(error, 'POST /api/users')
  }
}

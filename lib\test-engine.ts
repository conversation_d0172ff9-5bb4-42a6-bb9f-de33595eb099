import { QuestionType, Difficulty, EnglishLevel } from '@prisma/client'

export interface TestQuestion {
  id: string
  type: QuestionType
  content: string
  options?: any
  correctAnswer: any
  points: number
  difficulty: Difficulty
  timeLimit?: number
}

export interface StudentAnswer {
  questionId: string
  answer: any
  timeSpent?: number
}

export interface TestResult {
  totalQuestions: number
  correctAnswers: number
  score: number
  percentage: number
  passed: boolean
  timeSpent: number
  answers: AnswerResult[]
}

export interface AnswerResult {
  questionId: string
  answer: any
  isCorrect: boolean
  pointsEarned: number
  timeSpent?: number
}

export class TestEngine {
  private questions: TestQuestion[]
  private passingScore: number
  private totalTime: number

  constructor(questions: TestQuestion[], passingScore: number = 60, totalTime?: number) {
    this.questions = questions
    this.passingScore = passingScore
    this.totalTime = totalTime || 0
  }

  /**
   * Grade a single question
   */
  gradeQuestion(question: TestQuestion, studentAnswer: any): AnswerResult {
    const isCorrect = this.checkAnswer(question, studentAnswer)
    const pointsEarned = isCorrect ? question.points : 0

    return {
      questionId: question.id,
      answer: studentAnswer,
      isCorrect,
      pointsEarned,
    }
  }

  /**
   * Grade the entire test
   */
  gradeTest(studentAnswers: StudentAnswer[], timeSpent: number): TestResult {
    const answers: AnswerResult[] = []
    let totalPoints = 0
    let earnedPoints = 0

    for (const question of this.questions) {
      totalPoints += question.points
      
      const studentAnswer = studentAnswers.find(a => a.questionId === question.id)
      const answerResult = this.gradeQuestion(question, studentAnswer?.answer)
      
      if (studentAnswer?.timeSpent) {
        answerResult.timeSpent = studentAnswer.timeSpent
      }
      
      answers.push(answerResult)
      earnedPoints += answerResult.pointsEarned
    }

    const percentage = totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0
    const passed = percentage >= this.passingScore

    return {
      totalQuestions: this.questions.length,
      correctAnswers: answers.filter(a => a.isCorrect).length,
      score: earnedPoints,
      percentage: Math.round(percentage * 100) / 100,
      passed,
      timeSpent,
      answers
    }
  }

  /**
   * Check if an answer is correct based on question type
   */
  private checkAnswer(question: TestQuestion, studentAnswer: any): boolean {
    if (!studentAnswer) return false

    switch (question.type) {
      case QuestionType.MULTIPLE_CHOICE:
        return this.checkMultipleChoice(question, studentAnswer)
      
      case QuestionType.TRUE_FALSE:
        return this.checkTrueFalse(question, studentAnswer)
      
      case QuestionType.FILL_IN_BLANK:
        return this.checkFillInBlank(question, studentAnswer)
      
      case QuestionType.SHORT_ANSWER:
        return this.checkShortAnswer(question, studentAnswer)
      
      case QuestionType.MATCHING:
        return this.checkMatching(question, studentAnswer)
      
      case QuestionType.ORDERING:
        return this.checkOrdering(question, studentAnswer)
      
      default:
        return false
    }
  }

  private checkMultipleChoice(question: TestQuestion, studentAnswer: any): boolean {
    const correctAnswer = question.correctAnswer
    
    if (Array.isArray(correctAnswer)) {
      // Multiple correct answers
      if (!Array.isArray(studentAnswer)) return false
      return correctAnswer.every(answer => studentAnswer.includes(answer)) &&
             studentAnswer.every(answer => correctAnswer.includes(answer))
    } else {
      // Single correct answer
      return studentAnswer === correctAnswer
    }
  }

  private checkTrueFalse(question: TestQuestion, studentAnswer: any): boolean {
    return studentAnswer === question.correctAnswer
  }

  private checkFillInBlank(question: TestQuestion, studentAnswer: any): boolean {
    const correctAnswers = Array.isArray(question.correctAnswer) 
      ? question.correctAnswer 
      : [question.correctAnswer]
    
    const normalizedStudentAnswer = this.normalizeText(studentAnswer)
    
    return correctAnswers.some(answer => 
      this.normalizeText(answer) === normalizedStudentAnswer
    )
  }

  private checkShortAnswer(question: TestQuestion, studentAnswer: any): boolean {
    // For short answers, we'll use fuzzy matching
    const correctAnswers = Array.isArray(question.correctAnswer) 
      ? question.correctAnswer 
      : [question.correctAnswer]
    
    const normalizedStudentAnswer = this.normalizeText(studentAnswer)
    
    return correctAnswers.some(answer => {
      const normalizedCorrectAnswer = this.normalizeText(answer)
      return this.calculateSimilarity(normalizedStudentAnswer, normalizedCorrectAnswer) >= 0.8
    })
  }

  private checkMatching(question: TestQuestion, studentAnswer: any): boolean {
    if (!Array.isArray(studentAnswer) || !Array.isArray(question.correctAnswer)) {
      return false
    }
    
    return JSON.stringify(studentAnswer.sort()) === JSON.stringify(question.correctAnswer.sort())
  }

  private checkOrdering(question: TestQuestion, studentAnswer: any): boolean {
    if (!Array.isArray(studentAnswer) || !Array.isArray(question.correctAnswer)) {
      return false
    }
    
    return JSON.stringify(studentAnswer) === JSON.stringify(question.correctAnswer)
  }

  private normalizeText(text: string): string {
    return text?.toString().toLowerCase().trim().replace(/[^\w\s]/g, '') || ''
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }
}

/**
 * Calculate English level based on test performance
 */
export function calculateEnglishLevel(percentage: number, difficulty: Difficulty): EnglishLevel {
  if (difficulty === Difficulty.EASY) {
    if (percentage >= 90) return EnglishLevel.A2
    if (percentage >= 70) return EnglishLevel.A1
    return EnglishLevel.A1
  } else if (difficulty === Difficulty.MEDIUM) {
    if (percentage >= 90) return EnglishLevel.B2
    if (percentage >= 70) return EnglishLevel.B1
    if (percentage >= 50) return EnglishLevel.A2
    return EnglishLevel.A1
  } else { // HARD
    if (percentage >= 90) return EnglishLevel.C2
    if (percentage >= 80) return EnglishLevel.C1
    if (percentage >= 70) return EnglishLevel.B2
    if (percentage >= 60) return EnglishLevel.B1
    if (percentage >= 50) return EnglishLevel.A2
    return EnglishLevel.A1
  }
}

/**
 * Generate test statistics
 */
export function generateTestStatistics(results: TestResult[]): {
  averageScore: number
  averagePercentage: number
  passRate: number
  averageTime: number
  difficultyBreakdown: Record<string, number>
} {
  if (results.length === 0) {
    return {
      averageScore: 0,
      averagePercentage: 0,
      passRate: 0,
      averageTime: 0,
      difficultyBreakdown: {}
    }
  }

  const totalScore = results.reduce((sum, result) => sum + result.score, 0)
  const totalPercentage = results.reduce((sum, result) => sum + result.percentage, 0)
  const passedCount = results.filter(result => result.passed).length
  const totalTime = results.reduce((sum, result) => sum + result.timeSpent, 0)

  return {
    averageScore: Math.round((totalScore / results.length) * 100) / 100,
    averagePercentage: Math.round((totalPercentage / results.length) * 100) / 100,
    passRate: Math.round((passedCount / results.length) * 100),
    averageTime: Math.round(totalTime / results.length),
    difficultyBreakdown: {} // TODO: Implement difficulty breakdown
  }
}

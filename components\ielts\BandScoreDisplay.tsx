"use client"

import { IELTSSkill } from '@prisma/client'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CircularProgress } from '@/components/test/ProgressBar'
import { getBandScoreDescription, getSkillFeedback } from '@/lib/ielts-scoring'
import { cn } from '@/lib/utils'
import { 
  Headphones, 
  BookOpen, 
  PenTool, 
  Mic,
  Trophy,
  Target,
  TrendingUp
} from 'lucide-react'

interface IELTSScores {
  listening: number
  reading: number
  writing: number
  speaking: number
  overall: number
}

interface BandScoreDisplayProps {
  scores: IELTSScores
  targetScore?: number
  previousScore?: number
  showFeedback?: boolean
  className?: string
}

export function BandScoreDisplay({
  scores,
  targetScore,
  previousScore,
  showFeedback = true,
  className
}: BandScoreDisplayProps) {
  const skillIcons = {
    listening: Headphones,
    reading: BookOpen,
    writing: PenTool,
    speaking: Mic
  }

  const getBandColor = (score: number): string => {
    if (score >= 8.5) return 'text-green-600 bg-green-100'
    if (score >= 7.0) return 'text-blue-600 bg-blue-100'
    if (score >= 6.0) return 'text-yellow-600 bg-yellow-100'
    if (score >= 5.0) return 'text-orange-600 bg-orange-100'
    return 'text-red-600 bg-red-100'
  }

  const getScorePercentage = (score: number): number => {
    return (score / 9) * 100
  }

  const improvement = previousScore ? scores.overall - previousScore : null

  return (
    <div className={cn("space-y-6", className)}>
      {/* Overall Score */}
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center space-x-2">
            <Trophy className="h-6 w-6 text-yellow-500" />
            <span>Overall IELTS Band Score</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <div className="flex justify-center">
            <CircularProgress
              percentage={getScorePercentage(scores.overall)}
              size={150}
              color="#3b82f6"
              showPercentage={false}
            />
          </div>
          
          <div className="space-y-2">
            <div className="text-4xl font-bold text-blue-600">
              {scores.overall}
            </div>
            <Badge className={cn("text-sm", getBandColor(scores.overall))}>
              {getBandScoreDescription(scores.overall)}
            </Badge>
          </div>

          {/* Target and Improvement */}
          <div className="flex justify-center space-x-4 text-sm">
            {targetScore && (
              <div className="flex items-center space-x-1">
                <Target className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">Target: {targetScore}</span>
                {scores.overall >= targetScore && (
                  <Badge variant="default" className="ml-1">Achieved!</Badge>
                )}
              </div>
            )}
            
            {improvement !== null && (
              <div className="flex items-center space-x-1">
                <TrendingUp className={cn(
                  "h-4 w-4",
                  improvement >= 0 ? "text-green-500" : "text-red-500"
                )} />
                <span className={cn(
                  "font-medium",
                  improvement >= 0 ? "text-green-600" : "text-red-600"
                )}>
                  {improvement >= 0 ? "+" : ""}{improvement.toFixed(1)}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Individual Skills */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Object.entries(scores).filter(([skill]) => skill !== 'overall').map(([skill, score]) => {
          const Icon = skillIcons[skill as keyof typeof skillIcons]
          const skillName = skill.charAt(0).toUpperCase() + skill.slice(1)
          
          return (
            <Card key={skill}>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center space-x-2 text-base">
                  <Icon className="h-5 w-5" />
                  <span>{skillName}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-3">
                <div className="text-2xl font-bold text-gray-900">
                  {score}
                </div>
                <Badge className={cn("text-xs", getBandColor(score))}>
                  {getBandScoreDescription(score)}
                </Badge>
                
                {/* Progress bar */}
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getScorePercentage(score)}%` }}
                  />
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Detailed Feedback */}
      {showFeedback && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(scores).filter(([skill]) => skill !== 'overall').map(([skill, score]) => {
            const skillName = skill.charAt(0).toUpperCase() + skill.slice(1)
            const feedback = getSkillFeedback(skill as IELTSSkill, score)
            
            return (
              <Card key={`${skill}-feedback`}>
                <CardHeader>
                  <CardTitle className="text-base">{skillName} Feedback</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">{feedback}</p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}

interface SkillBreakdownProps {
  scores: IELTSScores
  skillDetails?: {
    [key: string]: {
      correctAnswers: number
      totalQuestions: number
      timeSpent?: number
    }
  }
  className?: string
}

export function SkillBreakdown({ scores, skillDetails, className }: SkillBreakdownProps) {
  const skills = [
    { key: 'listening', name: 'Listening', icon: Headphones },
    { key: 'reading', name: 'Reading', icon: BookOpen },
    { key: 'writing', name: 'Writing', icon: PenTool },
    { key: 'speaking', name: 'Speaking', icon: Mic }
  ]

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Skill Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {skills.map(({ key, name, icon: Icon }) => {
            const score = scores[key as keyof IELTSScores]
            const details = skillDetails?.[key]
            
            return (
              <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Icon className="h-5 w-5 text-gray-600" />
                  <div>
                    <div className="font-medium">{name}</div>
                    {details && (
                      <div className="text-sm text-gray-500">
                        {details.correctAnswers}/{details.totalQuestions} correct
                        {details.timeSpent && (
                          <span className="ml-2">
                            • {Math.round(details.timeSpent / 60)}min
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-lg font-bold">{score}</div>
                  <div className="text-xs text-gray-500">Band Score</div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

interface IELTSProgressProps {
  currentScores: IELTSScores
  previousScores?: IELTSScores
  targetScores?: Partial<IELTSScores>
  className?: string
}

export function IELTSProgress({ 
  currentScores, 
  previousScores, 
  targetScores,
  className 
}: IELTSProgressProps) {
  const skills = ['listening', 'reading', 'writing', 'speaking', 'overall'] as const

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Progress Tracking</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {skills.map((skill) => {
            const current = currentScores[skill]
            const previous = previousScores?.[skill]
            const target = targetScores?.[skill]
            const improvement = previous ? current - previous : null
            
            return (
              <div key={skill} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium capitalize">{skill}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold">{current}</span>
                    {improvement !== null && (
                      <span className={cn(
                        "text-sm font-medium",
                        improvement >= 0 ? "text-green-600" : "text-red-600"
                      )}>
                        ({improvement >= 0 ? "+" : ""}{improvement.toFixed(1)})
                      </span>
                    )}
                  </div>
                </div>
                
                {target && (
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={cn(
                        "h-2 rounded-full transition-all duration-300",
                        current >= target ? "bg-green-500" : "bg-blue-500"
                      )}
                      style={{ width: `${Math.min((current / target) * 100, 100)}%` }}
                    />
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

import { IELTSSkill, IELTSType } from '@prisma/client'

export interface IELTSScore {
  listening: number
  reading: number
  writing: number
  speaking: number
  overall: number
}

export interface IELTSResult {
  skill: IELTSSkill
  rawScore: number
  bandScore: number
  correctAnswers: number
  totalQuestions: number
}

/**
 * IELTS Band Score Conversion Tables
 * Based on official IELTS scoring guidelines
 */

// Listening Band Score Conversion (out of 40 questions)
const LISTENING_BAND_SCORES = [
  { min: 39, max: 40, band: 9.0 },
  { min: 37, max: 38, band: 8.5 },
  { min: 35, max: 36, band: 8.0 },
  { min: 32, max: 34, band: 7.5 },
  { min: 30, max: 31, band: 7.0 },
  { min: 26, max: 29, band: 6.5 },
  { min: 23, max: 25, band: 6.0 },
  { min: 18, max: 22, band: 5.5 },
  { min: 16, max: 17, band: 5.0 },
  { min: 13, max: 15, band: 4.5 },
  { min: 10, max: 12, band: 4.0 },
  { min: 8, max: 9, band: 3.5 },
  { min: 6, max: 7, band: 3.0 },
  { min: 4, max: 5, band: 2.5 },
  { min: 3, max: 3, band: 2.0 },
  { min: 2, max: 2, band: 1.5 },
  { min: 1, max: 1, band: 1.0 },
  { min: 0, max: 0, band: 0.0 }
]

// Reading Band Score Conversion - Academic (out of 40 questions)
const READING_ACADEMIC_BAND_SCORES = [
  { min: 39, max: 40, band: 9.0 },
  { min: 37, max: 38, band: 8.5 },
  { min: 35, max: 36, band: 8.0 },
  { min: 33, max: 34, band: 7.5 },
  { min: 30, max: 32, band: 7.0 },
  { min: 27, max: 29, band: 6.5 },
  { min: 23, max: 26, band: 6.0 },
  { min: 19, max: 22, band: 5.5 },
  { min: 15, max: 18, band: 5.0 },
  { min: 13, max: 14, band: 4.5 },
  { min: 10, max: 12, band: 4.0 },
  { min: 8, max: 9, band: 3.5 },
  { min: 6, max: 7, band: 3.0 },
  { min: 4, max: 5, band: 2.5 },
  { min: 3, max: 3, band: 2.0 },
  { min: 2, max: 2, band: 1.5 },
  { min: 1, max: 1, band: 1.0 },
  { min: 0, max: 0, band: 0.0 }
]

// Reading Band Score Conversion - General Training (out of 40 questions)
const READING_GENERAL_BAND_SCORES = [
  { min: 40, max: 40, band: 9.0 },
  { min: 39, max: 39, band: 8.5 },
  { min: 37, max: 38, band: 8.0 },
  { min: 36, max: 36, band: 7.5 },
  { min: 34, max: 35, band: 7.0 },
  { min: 32, max: 33, band: 6.5 },
  { min: 30, max: 31, band: 6.0 },
  { min: 27, max: 29, band: 5.5 },
  { min: 23, max: 26, band: 5.0 },
  { min: 19, max: 22, band: 4.5 },
  { min: 15, max: 18, band: 4.0 },
  { min: 12, max: 14, band: 3.5 },
  { min: 9, max: 11, band: 3.0 },
  { min: 6, max: 8, band: 2.5 },
  { min: 4, max: 5, band: 2.0 },
  { min: 3, max: 3, band: 1.5 },
  { min: 1, max: 2, band: 1.0 },
  { min: 0, max: 0, band: 0.0 }
]

/**
 * Convert raw score to IELTS band score for Listening
 */
export function calculateListeningBandScore(correctAnswers: number, totalQuestions: number = 40): number {
  // Normalize to 40 questions if different total
  const normalizedScore = totalQuestions !== 40 
    ? Math.round((correctAnswers / totalQuestions) * 40)
    : correctAnswers

  const bandScore = LISTENING_BAND_SCORES.find(
    score => normalizedScore >= score.min && normalizedScore <= score.max
  )

  return bandScore?.band || 0.0
}

/**
 * Convert raw score to IELTS band score for Reading
 */
export function calculateReadingBandScore(
  correctAnswers: number, 
  ieltsType: IELTSType, 
  totalQuestions: number = 40
): number {
  // Normalize to 40 questions if different total
  const normalizedScore = totalQuestions !== 40 
    ? Math.round((correctAnswers / totalQuestions) * 40)
    : correctAnswers

  const bandScores = ieltsType === IELTSType.ACADEMIC 
    ? READING_ACADEMIC_BAND_SCORES 
    : READING_GENERAL_BAND_SCORES

  const bandScore = bandScores.find(
    score => normalizedScore >= score.min && normalizedScore <= score.max
  )

  return bandScore?.band || 0.0
}

/**
 * Calculate Writing band score based on criteria
 * This is a simplified version - in real IELTS, human examiners assess multiple criteria
 */
export function calculateWritingBandScore(criteria: {
  taskAchievement: number // 0-9
  coherenceCohesion: number // 0-9
  lexicalResource: number // 0-9
  grammaticalRange: number // 0-9
}): number {
  const average = (
    criteria.taskAchievement + 
    criteria.coherenceCohesion + 
    criteria.lexicalResource + 
    criteria.grammaticalRange
  ) / 4

  // Round to nearest 0.5
  return Math.round(average * 2) / 2
}

/**
 * Calculate Speaking band score based on criteria
 * This is a simplified version - in real IELTS, human examiners assess multiple criteria
 */
export function calculateSpeakingBandScore(criteria: {
  fluencyCoherence: number // 0-9
  lexicalResource: number // 0-9
  grammaticalRange: number // 0-9
  pronunciation: number // 0-9
}): number {
  const average = (
    criteria.fluencyCoherence + 
    criteria.lexicalResource + 
    criteria.grammaticalRange + 
    criteria.pronunciation
  ) / 4

  // Round to nearest 0.5
  return Math.round(average * 2) / 2
}

/**
 * Calculate overall IELTS band score
 */
export function calculateOverallBandScore(scores: {
  listening: number
  reading: number
  writing: number
  speaking: number
}): number {
  const average = (scores.listening + scores.reading + scores.writing + scores.speaking) / 4
  
  // Round to nearest 0.5
  return Math.round(average * 2) / 2
}

/**
 * Get IELTS band score description
 */
export function getBandScoreDescription(bandScore: number): string {
  const descriptions: Record<number, string> = {
    9.0: "Expert user",
    8.5: "Very good user",
    8.0: "Very good user", 
    7.5: "Good user",
    7.0: "Good user",
    6.5: "Competent user",
    6.0: "Competent user",
    5.5: "Modest user",
    5.0: "Modest user",
    4.5: "Limited user",
    4.0: "Limited user",
    3.5: "Extremely limited user",
    3.0: "Extremely limited user",
    2.5: "Intermittent user",
    2.0: "Intermittent user",
    1.5: "Non-user",
    1.0: "Non-user",
    0.0: "Did not attempt the test"
  }

  return descriptions[bandScore] || "Invalid score"
}

/**
 * Calculate complete IELTS result
 */
export function calculateIELTSResult(results: {
  listening: { correctAnswers: number; totalQuestions: number }
  reading: { correctAnswers: number; totalQuestions: number; type: IELTSType }
  writing: { 
    taskAchievement: number
    coherenceCohesion: number
    lexicalResource: number
    grammaticalRange: number
  }
  speaking: {
    fluencyCoherence: number
    lexicalResource: number
    grammaticalRange: number
    pronunciation: number
  }
}): IELTSScore {
  const listening = calculateListeningBandScore(
    results.listening.correctAnswers, 
    results.listening.totalQuestions
  )

  const reading = calculateReadingBandScore(
    results.reading.correctAnswers,
    results.reading.type,
    results.reading.totalQuestions
  )

  const writing = calculateWritingBandScore(results.writing)
  const speaking = calculateSpeakingBandScore(results.speaking)

  const overall = calculateOverallBandScore({
    listening,
    reading,
    writing,
    speaking
  })

  return {
    listening,
    reading,
    writing,
    speaking,
    overall
  }
}

/**
 * Get skill-specific feedback based on band score
 */
export function getSkillFeedback(skill: IELTSSkill, bandScore: number): string {
  const feedbackMap: Record<IELTSSkill, Record<string, string>> = {
    [IELTSSkill.LISTENING]: {
      "9.0": "Excellent listening skills with full understanding of complex spoken English",
      "8.0-8.5": "Very good listening skills with occasional minor difficulties",
      "7.0-7.5": "Good listening skills with some difficulty in complex situations",
      "6.0-6.5": "Competent listening skills but may miss some details",
      "5.0-5.5": "Modest listening skills with frequent difficulties",
      "4.0-4.5": "Limited listening skills requiring significant improvement",
      "below-4.0": "Very limited listening skills requiring extensive practice"
    },
    [IELTSSkill.READING]: {
      "9.0": "Excellent reading skills with full comprehension of complex texts",
      "8.0-8.5": "Very good reading skills with minor comprehension issues",
      "7.0-7.5": "Good reading skills with some difficulty in complex texts",
      "6.0-6.5": "Competent reading skills but may struggle with academic texts",
      "5.0-5.5": "Modest reading skills with frequent comprehension difficulties",
      "4.0-4.5": "Limited reading skills requiring significant improvement",
      "below-4.0": "Very limited reading skills requiring extensive practice"
    },
    [IELTSSkill.WRITING]: {
      "9.0": "Excellent writing skills with sophisticated language use",
      "8.0-8.5": "Very good writing skills with minor language errors",
      "7.0-7.5": "Good writing skills with some language limitations",
      "6.0-6.5": "Competent writing skills but needs improvement in accuracy",
      "5.0-5.5": "Modest writing skills with frequent errors affecting clarity",
      "4.0-4.5": "Limited writing skills requiring significant improvement",
      "below-4.0": "Very limited writing skills requiring extensive practice"
    },
    [IELTSSkill.SPEAKING]: {
      "9.0": "Excellent speaking skills with natural and effortless communication",
      "8.0-8.5": "Very good speaking skills with minor pronunciation issues",
      "7.0-7.5": "Good speaking skills with some hesitation and errors",
      "6.0-6.5": "Competent speaking skills but lacks fluency at times",
      "5.0-5.5": "Modest speaking skills with frequent pauses and errors",
      "4.0-4.5": "Limited speaking skills requiring significant improvement",
      "below-4.0": "Very limited speaking skills requiring extensive practice"
    }
  }

  const skillFeedback = feedbackMap[skill]
  
  if (bandScore >= 9.0) return skillFeedback["9.0"]
  if (bandScore >= 8.0) return skillFeedback["8.0-8.5"]
  if (bandScore >= 7.0) return skillFeedback["7.0-7.5"]
  if (bandScore >= 6.0) return skillFeedback["6.0-6.5"]
  if (bandScore >= 5.0) return skillFeedback["5.0-5.5"]
  if (bandScore >= 4.0) return skillFeedback["4.0-4.5"]
  return skillFeedback["below-4.0"]
}

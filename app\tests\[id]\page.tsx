'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  ArrowLeft,
  Play,
  Edit,
  Users,
  Clock,
  Target,
  BarChart3,
  Settings,
  Award,
  FileText
} from 'lucide-react'

interface Test {
  id: string
  title: string
  description: string
  type: string
  level: string
  duration: number
  passingScore: number
  maxAttempts: number
  shuffleQuestions: boolean
  showResults: boolean
  isPublic: boolean
  ieltsType?: string
  bandScoring: boolean
  createdAt: string
  creator: {
    name: string
  }
  sections: Array<{
    id: string
    title: string
    description: string
    order: number
    duration?: number
    _count: {
      questions: number
    }
  }>
  _count: {
    results: number
    sections: number
  }
}

export default function TestDetailPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const [test, setTest] = useState<Test | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchTest()
    }
  }, [params.id])

  const fetchTest = async () => {
    try {
      const response = await fetch(`/api/tests/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setTest(data.test)
      } else if (response.status === 404) {
        router.push('/tests')
      }
    } catch (error) {
      console.error('Failed to fetch test:', error)
    } finally {
      setLoading(false)
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'LEVEL_TEST': return 'bg-blue-100 text-blue-800'
      case 'IELTS_MOCK': return 'bg-purple-100 text-purple-800'
      case 'PRACTICE_TEST': return 'bg-green-100 text-green-800'
      case 'CUSTOM_TEST': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'A1': return 'bg-red-100 text-red-800'
      case 'A2': return 'bg-orange-100 text-orange-800'
      case 'B1': return 'bg-yellow-100 text-yellow-800'
      case 'B2': return 'bg-green-100 text-green-800'
      case 'C1': return 'bg-blue-100 text-blue-800'
      case 'C2': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const canEdit = session?.user?.role === 'ADMIN' || 
                 (session?.user?.role === 'TEACHER' && test?.creator.name === session.user.name)

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="w-10 h-10 bg-gray-200 rounded animate-pulse"></div>
          <div className="space-y-2">
            <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-48"></div>
                <div className="h-4 bg-gray-200 rounded w-32"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  if (!test) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/tests">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Test Not Found</h1>
            <p className="text-muted-foreground">
              The test you're looking for doesn't exist or you don't have access to it.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/tests">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold">{test.title}</h1>
          <p className="text-muted-foreground">
            Created by {test.creator.name} • {new Date(test.createdAt).toLocaleDateString()}
          </p>
        </div>
        <div className="flex gap-2">
          {canEdit && (
            <>
              <Link href={`/tests/${test.id}/questions`}>
                <Button variant="outline">
                  <Edit className="mr-2 h-4 w-4" />
                  Manage Questions
                </Button>
              </Link>
              <Link href={`/tests/${test.id}/edit`}>
                <Button variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Button>
              </Link>
            </>
          )}
          <Link href={`/tests/${test.id}/take`}>
            <Button>
              <Play className="mr-2 h-4 w-4" />
              Take Test
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Test Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Test Overview</CardTitle>
              <CardDescription>
                {test.description || 'No description provided'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 mb-4">
                <Badge className={getTypeColor(test.type)}>
                  {test.type.replace('_', ' ')}
                </Badge>
                {test.level && (
                  <Badge className={getLevelColor(test.level)}>
                    {test.level}
                  </Badge>
                )}
                {test.ieltsType && (
                  <Badge variant="outline">
                    {test.ieltsType.replace('_', ' ')}
                  </Badge>
                )}
                {test.isPublic && (
                  <Badge variant="outline">Public</Badge>
                )}
                {test.bandScoring && (
                  <Badge variant="outline">Band Scoring</Badge>
                )}
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-2">
                    <Clock className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="text-2xl font-bold">{test.duration}</div>
                  <div className="text-sm text-muted-foreground">Minutes</div>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-2">
                    <Target className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="text-2xl font-bold">{test.passingScore}%</div>
                  <div className="text-sm text-muted-foreground">Passing</div>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-2">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="text-2xl font-bold">{test._count.results}</div>
                  <div className="text-sm text-muted-foreground">Attempts</div>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mb-2">
                    <FileText className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="text-2xl font-bold">{test._count.sections}</div>
                  <div className="text-sm text-muted-foreground">Sections</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Test Sections */}
          <Card>
            <CardHeader>
              <CardTitle>Test Sections</CardTitle>
              <CardDescription>
                Overview of all sections in this test
              </CardDescription>
            </CardHeader>
            <CardContent>
              {test.sections.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No sections yet</h3>
                  <p className="text-muted-foreground mb-4">
                    This test doesn't have any sections. Add sections to include questions.
                  </p>
                  {canEdit && (
                    <Link href={`/tests/${test.id}/sections/create`}>
                      <Button>Add Section</Button>
                    </Link>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {test.sections
                    .sort((a, b) => a.order - b.order)
                    .map((section) => (
                      <div key={section.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-semibold">{section.title}</h4>
                            {section.description && (
                              <p className="text-sm text-muted-foreground">
                                {section.description}
                              </p>
                            )}
                          </div>
                          <Badge variant="outline">
                            {section._count.questions} questions
                          </Badge>
                        </div>
                        {section.duration && (
                          <div className="text-sm text-muted-foreground">
                            Duration: {section.duration} minutes
                          </div>
                        )}
                      </div>
                    ))}
                  {canEdit && (
                    <Link href={`/tests/${test.id}/sections/create`}>
                      <Button variant="outline" className="w-full">
                        Add Section
                      </Button>
                    </Link>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href={`/tests/${test.id}/take`}>
                <Button className="w-full">
                  <Play className="mr-2 h-4 w-4" />
                  Take Test
                </Button>
              </Link>
              
              {canEdit && (
                <>
                  <Link href={`/tests/${test.id}/questions`}>
                    <Button variant="outline" className="w-full">
                      <Edit className="mr-2 h-4 w-4" />
                      Manage Questions
                    </Button>
                  </Link>
                  <Link href={`/tests/${test.id}/edit`}>
                    <Button variant="outline" className="w-full">
                      <Settings className="mr-2 h-4 w-4" />
                      Test Settings
                    </Button>
                  </Link>
                  <Link href={`/tests/${test.id}/analytics`}>
                    <Button variant="outline" className="w-full">
                      <BarChart3 className="mr-2 h-4 w-4" />
                      View Analytics
                    </Button>
                  </Link>
                </>
              )}
            </CardContent>
          </Card>

          {/* Test Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Test Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Max Attempts:</span>
                <span>{test.maxAttempts}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Shuffle Questions:</span>
                <span>{test.shuffleQuestions ? 'Yes' : 'No'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Show Results:</span>
                <span>{test.showResults ? 'Yes' : 'No'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Public:</span>
                <span>{test.isPublic ? 'Yes' : 'No'}</span>
              </div>
              {test.type === 'IELTS_MOCK' && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Band Scoring:</span>
                  <span>{test.bandScoring ? 'Yes' : 'No'}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Activity */}
          {test._count.results > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  {test._count.results} students have taken this test
                </div>
                {canEdit && (
                  <Link href={`/tests/${test.id}/results`} className="mt-2 inline-block">
                    <Button variant="outline" size="sm">
                      View All Results
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

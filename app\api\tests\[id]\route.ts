import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const test = await db.test.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: {
            name: true,
            id: true
          }
        },
        sections: {
          include: {
            _count: {
              select: {
                questions: true
              }
            }
          },
          orderBy: {
            order: 'asc'
          }
        },
        _count: {
          select: {
            results: true,
            sections: true
          }
        }
      }
    })

    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 })
    }

    // Check access permissions
    const canAccess = 
      test.isPublic ||
      session.user.role === 'ADMIN' ||
      test.creator.id === session.user.id ||
      (session.user.role === 'STUDENT' && await checkStudentAccess(test.id, session.user.id))

    if (!canAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    return NextResponse.json({ test })
  } catch (error) {
    console.error('Failed to fetch test:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const test = await db.test.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: { id: true }
        }
      }
    })

    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 })
    }

    // Check edit permissions
    const canEdit = 
      session.user.role === 'ADMIN' ||
      test.creator.id === session.user.id

    if (!canEdit) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const body = await request.json()
    const {
      title,
      description,
      type,
      level,
      duration,
      passingScore,
      maxAttempts,
      shuffleQuestions,
      showResults,
      isPublic,
      ieltsType,
      bandScoring
    } = body

    const updatedTest = await db.test.update({
      where: { id: params.id },
      data: {
        title,
        description,
        type,
        level,
        duration,
        passingScore,
        maxAttempts,
        shuffleQuestions,
        showResults,
        isPublic,
        ieltsType,
        bandScoring
      },
      include: {
        creator: {
          select: {
            name: true
          }
        }
      }
    })

    return NextResponse.json({ test: updatedTest })
  } catch (error) {
    console.error('Failed to update test:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const test = await db.test.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: { id: true }
        }
      }
    })

    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 })
    }

    // Check delete permissions
    const canDelete = 
      session.user.role === 'ADMIN' ||
      test.creator.id === session.user.id

    if (!canDelete) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    await db.test.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Test deleted successfully' })
  } catch (error) {
    console.error('Failed to delete test:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to check if a student has access to a test
async function checkStudentAccess(testId: string, studentId: string): Promise<boolean> {
  const assignment = await db.testAssignment.findFirst({
    where: {
      testId,
      class: {
        students: {
          some: {
            studentId
          }
        }
      }
    }
  })

  return !!assignment
}

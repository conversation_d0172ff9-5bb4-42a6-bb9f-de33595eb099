import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { TestEngine } from '@/lib/test-engine'
import { calculateIELTSResult } from '@/lib/ielts-scoring'
import { TestType, IELTSType } from '@prisma/client'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const testId = params.id
    const body = await request.json()
    const { answers, timeSpent } = body

    // Validate input
    if (!answers || typeof timeSpent !== 'number') {
      return NextResponse.json(
        { error: 'Answers and timeSpent are required' },
        { status: 400 }
      )
    }

    // Get the test with questions
    const test = await db.test.findUnique({
      where: { id: testId },
      include: {
        sections: {
          include: {
            questions: {
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    })

    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 })
    }

    // Check if user has permission to take this test
    const canTakeTest = await checkTestPermission(testId, session.user.id, session.user.role)
    if (!canTakeTest) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if user has exceeded max attempts
    const existingResults = await db.testResult.count({
      where: {
        testId,
        userId: session.user.id
      }
    })

    if (existingResults >= test.maxAttempts) {
      return NextResponse.json(
        { error: 'Maximum attempts exceeded' },
        { status: 400 }
      )
    }

    // Flatten questions from all sections
    const allQuestions = test.sections.flatMap(section => section.questions)

    // Prepare questions for test engine
    const testQuestions = allQuestions.map(q => ({
      id: q.id,
      type: q.type,
      content: q.content,
      options: q.options,
      correctAnswer: q.correctAnswer,
      points: q.points,
      difficulty: q.difficulty
    }))

    // Grade the test
    const testEngine = new TestEngine(testQuestions, test.passingScore)
    const studentAnswers = Object.entries(answers).map(([questionId, answer]) => ({
      questionId,
      answer
    }))

    const result = testEngine.gradeTest(studentAnswers, timeSpent)

    // Calculate IELTS scores if applicable
    let ieltsScores = null
    if (test.type === TestType.IELTS_MOCK && test.bandScoring) {
      ieltsScores = await calculateIELTSScores(test, result, test.ieltsType || IELTSType.ACADEMIC)
    }

    // Save the test result
    const testResult = await db.testResult.create({
      data: {
        testId,
        userId: session.user.id,
        attempt: existingResults + 1,
        totalQuestions: result.totalQuestions,
        correctAnswers: result.correctAnswers,
        score: result.score,
        percentage: result.percentage,
        passed: result.passed,
        timeSpent,
        startedAt: new Date(Date.now() - timeSpent * 1000),
        completedAt: new Date(),
        status: 'COMPLETED',
        // IELTS specific scores
        listeningScore: ieltsScores?.listening,
        readingScore: ieltsScores?.reading,
        writingScore: ieltsScores?.writing,
        speakingScore: ieltsScores?.speaking,
        overallBandScore: ieltsScores?.overall
      }
    })

    // Save individual answers
    const answerData = result.answers.map(answer => ({
      questionId: answer.questionId,
      testResultId: testResult.id,
      answer: answer.answer,
      isCorrect: answer.isCorrect,
      pointsEarned: answer.pointsEarned,
      timeSpent: answer.timeSpent
    }))

    await db.answer.createMany({
      data: answerData
    })

    // Return the result
    return NextResponse.json({
      result: {
        id: testResult.id,
        ...result,
        ieltsScores,
        attempt: testResult.attempt
      }
    })

  } catch (error) {
    console.error('Failed to submit test:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function checkTestPermission(testId: string, userId: string, userRole: string): Promise<boolean> {
  if (userRole === 'ADMIN') {
    return true
  }

  const test = await db.test.findUnique({
    where: { id: testId },
    include: {
      assignments: {
        include: {
          class: {
            include: {
              students: true
            }
          }
        }
      }
    }
  })

  if (!test) {
    return false
  }

  // Check if test is public
  if (test.isPublic) {
    return true
  }

  // Check if user is the creator
  if (test.creatorId === userId) {
    return true
  }

  // Check if user is assigned to this test through a class
  const isAssigned = test.assignments.some(assignment =>
    assignment.class?.students.some(student => student.studentId === userId)
  )

  return isAssigned
}

async function calculateIELTSScores(test: any, result: any, ieltsType: IELTSType) {
  // This is a simplified IELTS scoring calculation
  // In a real implementation, you would need more sophisticated logic
  // based on the specific sections and question types

  const sectionResults = test.sections.map((section: any) => {
    const sectionQuestions = section.questions
    const sectionAnswers = result.answers.filter((answer: any) =>
      sectionQuestions.some((q: any) => q.id === answer.questionId)
    )
    
    const correctAnswers = sectionAnswers.filter((a: any) => a.isCorrect).length
    const totalQuestions = sectionQuestions.length

    return {
      skill: section.skill,
      correctAnswers,
      totalQuestions
    }
  })

  // For now, return a simplified calculation
  // This should be replaced with proper IELTS scoring logic
  const listening = sectionResults.find(s => s.skill === 'LISTENING')
  const reading = sectionResults.find(s => s.skill === 'READING')
  
  if (listening && reading) {
    const { calculateIELTSResult } = await import('@/lib/ielts-scoring')
    
    return calculateIELTSResult({
      listening: {
        correctAnswers: listening.correctAnswers,
        totalQuestions: listening.totalQuestions
      },
      reading: {
        correctAnswers: reading.correctAnswers,
        totalQuestions: reading.totalQuestions,
        type: ieltsType
      },
      writing: {
        taskAchievement: 6.0, // These would come from manual grading
        coherenceCohesion: 6.0,
        lexicalResource: 6.0,
        grammaticalRange: 6.0
      },
      speaking: {
        fluencyCoherence: 6.0, // These would come from manual grading
        lexicalResource: 6.0,
        grammaticalRange: 6.0,
        pronunciation: 6.0
      }
    })
  }

  return null
}

'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Plus, Trash2, Lightbulb } from 'lucide-react'

interface FillInBlankBuilderProps {
  content: string
  correctAnswer: any
  options: any
  onContentChange: (content: string) => void
  onCorrectAnswerChange: (correctAnswer: any) => void
  onOptionsChange: (options: any) => void
  error?: string
}

export function FillInBlankBuilder({
  content,
  correctAnswer,
  options,
  onContentChange,
  onCorrectAnswerChange,
  onOptionsChange,
  error
}: FillInBlankBuilderProps) {
  const [answers, setAnswers] = useState<string[]>([''])
  const [caseSensitive, setCaseSensitive] = useState(false)
  const [exactMatch, setExactMatch] = useState(false)
  const [showHints, setShowHints] = useState(false)
  const [hints, setHints] = useState<string[]>([''])

  useEffect(() => {
    if (correctAnswer?.answers) {
      setAnswers(correctAnswer.answers)
    } else if (correctAnswer?.answer) {
      setAnswers([correctAnswer.answer])
    }
    
    if (options) {
      setCaseSensitive(options.caseSensitive || false)
      setExactMatch(options.exactMatch || false)
      setShowHints(options.showHints || false)
      if (options.hints) {
        setHints(options.hints)
      }
    }
  }, [correctAnswer, options])

  useEffect(() => {
    onCorrectAnswerChange({
      answer: answers[0] || '',
      answers: answers.filter(answer => answer.trim() !== '')
    })
  }, [answers, onCorrectAnswerChange])

  useEffect(() => {
    onOptionsChange({
      caseSensitive,
      exactMatch,
      showHints,
      hints: showHints ? hints.filter(hint => hint.trim() !== '') : []
    })
  }, [caseSensitive, exactMatch, showHints, hints, onOptionsChange])

  const addAnswer = () => {
    setAnswers([...answers, ''])
  }

  const removeAnswer = (index: number) => {
    if (answers.length > 1) {
      setAnswers(answers.filter((_, i) => i !== index))
    }
  }

  const updateAnswer = (index: number, value: string) => {
    const newAnswers = [...answers]
    newAnswers[index] = value
    setAnswers(newAnswers)
  }

  const addHint = () => {
    setHints([...hints, ''])
  }

  const removeHint = (index: number) => {
    if (hints.length > 1) {
      setHints(hints.filter((_, i) => i !== index))
    }
  }

  const updateHint = (index: number, value: string) => {
    const newHints = [...hints]
    newHints[index] = value
    setHints(newHints)
  }

  const insertBlank = () => {
    const textarea = document.getElementById('content') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newContent = content.substring(0, start) + '______' + content.substring(end)
      onContentChange(newContent)
      
      // Set cursor position after the blank
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + 6, start + 6)
      }, 0)
    }
  }

  const blankCount = (content.match(/_{3,}/g) || []).length

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fill-in-the-Blank Configuration</CardTitle>
        <CardDescription>
          Set up the correct answers and validation options
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        {/* Question Text with Blank Helper */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="content">Question Text with Blanks</Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={insertBlank}
            >
              Insert Blank (______)
            </Button>
          </div>
          <Textarea
            id="content"
            value={content}
            onChange={(e) => onContentChange(e.target.value)}
            placeholder="Enter your question here. Use ______ (6 underscores) to create blanks."
            rows={4}
          />
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Lightbulb className="h-4 w-4" />
            <span>
              Use 6 or more underscores (______) to create fill-in-the-blank spaces.
              {blankCount > 0 && ` Found ${blankCount} blank${blankCount > 1 ? 's' : ''}.`}
            </span>
          </div>
        </div>

        {/* Correct Answers */}
        <div className="space-y-3">
          <Label>Correct Answers</Label>
          <p className="text-sm text-muted-foreground">
            Provide all acceptable answers. Students need to match at least one.
          </p>
          
          {answers.map((answer, index) => (
            <div key={index} className="flex items-center gap-2">
              <Badge variant="outline" className="min-w-[60px] justify-center">
                {index + 1}
              </Badge>
              <Input
                value={answer}
                onChange={(e) => updateAnswer(index, e.target.value)}
                placeholder={`Correct answer ${index + 1}`}
                className="flex-1"
              />
              {answers.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeAnswer(index)}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}
          
          <Button
            type="button"
            variant="outline"
            onClick={addAnswer}
            className="w-full"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Alternative Answer
          </Button>
        </div>

        {/* Validation Options */}
        <div className="space-y-4 pt-4 border-t">
          <Label className="text-base font-medium">Validation Settings</Label>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="caseSensitive">Case Sensitive</Label>
              <p className="text-sm text-muted-foreground">
                Require exact capitalization match
              </p>
            </div>
            <Switch
              id="caseSensitive"
              checked={caseSensitive}
              onCheckedChange={setCaseSensitive}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="exactMatch">Exact Match</Label>
              <p className="text-sm text-muted-foreground">
                Require exact spelling (no partial credit)
              </p>
            </div>
            <Switch
              id="exactMatch"
              checked={exactMatch}
              onCheckedChange={setExactMatch}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="showHints">Show Hints</Label>
              <p className="text-sm text-muted-foreground">
                Provide hints to help students
              </p>
            </div>
            <Switch
              id="showHints"
              checked={showHints}
              onCheckedChange={setShowHints}
            />
          </div>
        </div>

        {/* Hints Section */}
        {showHints && (
          <div className="space-y-3 pt-4 border-t">
            <Label>Hints (Optional)</Label>
            <p className="text-sm text-muted-foreground">
              Provide helpful hints that students can reveal if needed.
            </p>
            
            {hints.map((hint, index) => (
              <div key={index} className="flex items-center gap-2">
                <Badge variant="outline" className="min-w-[60px] justify-center">
                  Hint {index + 1}
                </Badge>
                <Input
                  value={hint}
                  onChange={(e) => updateHint(index, e.target.value)}
                  placeholder={`Hint ${index + 1}`}
                  className="flex-1"
                />
                {hints.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeHint(index)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
            
            <Button
              type="button"
              variant="outline"
              onClick={addHint}
              className="w-full"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Hint
            </Button>
          </div>
        )}

        {/* Preview */}
        <div className="pt-4 border-t">
          <Label className="text-sm font-medium">Preview</Label>
          <div className="mt-2 p-4 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium mb-3">Student will see:</p>
            <div className="space-y-2">
              <p className="text-sm">
                {content.replace(/_{3,}/g, '___[input field]___')}
              </p>
              {answers.filter(a => a.trim()).length > 0 && (
                <div className="text-xs text-muted-foreground">
                  <strong>Accepted answers:</strong> {answers.filter(a => a.trim()).join(', ')}
                </div>
              )}
              {showHints && hints.filter(h => h.trim()).length > 0 && (
                <div className="text-xs text-muted-foreground">
                  <strong>Hints available:</strong> {hints.filter(h => h.trim()).length}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

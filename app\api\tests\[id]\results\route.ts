import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const testId = params.id
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    // Check permissions
    const test = await db.test.findUnique({
      where: { id: testId },
      select: {
        id: true,
        title: true,
        creatorId: true,
        isPublic: true
      }
    })

    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 })
    }

    // Build where clause based on permissions
    const where: any = { testId }

    if (session.user.role === 'STUDENT') {
      // Students can only see their own results
      where.userId = session.user.id
    } else if (session.user.role === 'TEACHER') {
      // Teachers can see results for their own tests
      if (test.creatorId !== session.user.id) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
      // If userId is specified, filter by that user
      if (userId) {
        where.userId = userId
      }
    }
    // Admins can see all results

    // If userId is specified and user has permission, filter by that user
    if (userId && (session.user.role === 'ADMIN' || session.user.role === 'TEACHER')) {
      where.userId = userId
    }

    const skip = (page - 1) * limit

    const [results, total] = await Promise.all([
      db.testResult.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          answers: {
            include: {
              question: {
                select: {
                  id: true,
                  content: true,
                  type: true,
                  correctAnswer: true,
                  points: true
                }
              }
            }
          }
        },
        orderBy: {
          submittedAt: 'desc'
        },
        skip,
        take: limit
      }),
      db.testResult.count({ where })
    ])

    // Calculate statistics if user is teacher or admin
    let statistics = null
    if (session.user.role !== 'STUDENT') {
      const allResults = await db.testResult.findMany({
        where: { testId },
        select: {
          score: true,
          percentage: true,
          passed: true,
          timeSpent: true
        }
      })

      if (allResults.length > 0) {
        const totalScore = allResults.reduce((sum, r) => sum + r.score, 0)
        const totalPercentage = allResults.reduce((sum, r) => sum + r.percentage, 0)
        const passedCount = allResults.filter(r => r.passed).length
        const totalTime = allResults.reduce((sum, r) => sum + r.timeSpent, 0)

        statistics = {
          totalAttempts: allResults.length,
          averageScore: Math.round((totalScore / allResults.length) * 100) / 100,
          averagePercentage: Math.round((totalPercentage / allResults.length) * 100) / 100,
          passRate: Math.round((passedCount / allResults.length) * 100),
          averageTime: Math.round(totalTime / allResults.length),
          highestScore: Math.max(...allResults.map(r => r.score)),
          lowestScore: Math.min(...allResults.map(r => r.score))
        }
      }
    }

    return NextResponse.json({
      results,
      statistics,
      test: {
        id: test.id,
        title: test.title
      },
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Failed to fetch test results:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get detailed result for a specific attempt
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const testId = params.id
    const body = await request.json()
    const { resultId } = body

    if (!resultId) {
      return NextResponse.json(
        { error: 'Result ID is required' },
        { status: 400 }
      )
    }

    // Get the detailed result
    const result = await db.testResult.findUnique({
      where: { id: resultId },
      include: {
        test: {
          select: {
            id: true,
            title: true,
            creatorId: true,
            showResults: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        answers: {
          include: {
            question: {
              select: {
                id: true,
                content: true,
                type: true,
                options: true,
                correctAnswer: true,
                explanation: true,
                points: true
              }
            }
          },
          orderBy: {
            question: {
              order: 'asc'
            }
          }
        }
      }
    })

    if (!result) {
      return NextResponse.json({ error: 'Result not found' }, { status: 404 })
    }

    // Check permissions
    const canView = 
      session.user.role === 'ADMIN' ||
      result.userId === session.user.id ||
      (session.user.role === 'TEACHER' && result.test.creatorId === session.user.id)

    if (!canView) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if results should be shown
    if (!result.test.showResults && result.userId === session.user.id && session.user.role === 'STUDENT') {
      return NextResponse.json(
        { error: 'Results are not available for this test' },
        { status: 403 }
      )
    }

    return NextResponse.json({ result })

  } catch (error) {
    console.error('Failed to fetch detailed result:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

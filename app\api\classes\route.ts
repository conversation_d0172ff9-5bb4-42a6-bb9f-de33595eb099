import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const level = searchParams.get('level')

    const skip = (page - 1) * limit

    // Build where clause based on user role
    const where: any = {}

    if (session.user.role === 'STUDENT') {
      // Students can only see classes they're enrolled in
      where.students = {
        some: {
          studentId: session.user.id,
          status: 'ACTIVE'
        }
      }
    } else if (session.user.role === 'TEACHER') {
      // Teachers can see classes they teach
      where.teacherId = session.user.id
    } else if (session.user.organizationId) {
      // Admins can see all classes in their organization
      where.organizationId = session.user.organizationId
    }

    // Apply filters
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }
    if (level) {
      where.level = level
    }

    const [classes, total] = await Promise.all([
      db.class.findMany({
        where,
        include: {
          teacher: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          organization: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              students: true,
              assignedTests: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      db.class.count({ where })
    ])

    return NextResponse.json({
      classes,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Failed to fetch classes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers and admins can create classes
    if (session.user.role === 'STUDENT') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const {
      name,
      description,
      level,
      startDate,
      endDate,
      maxStudents
    } = body

    // Validation
    if (!name || !level || !startDate) {
      return NextResponse.json(
        { error: 'Name, level, and start date are required' },
        { status: 400 }
      )
    }

    // Validate dates
    const start = new Date(startDate)
    const end = endDate ? new Date(endDate) : null

    if (end && end <= start) {
      return NextResponse.json(
        { error: 'End date must be after start date' },
        { status: 400 }
      )
    }

    const classData = await db.class.create({
      data: {
        name,
        description,
        level,
        startDate: start,
        endDate: end,
        maxStudents: maxStudents || 30,
        teacherId: session.user.id,
        organizationId: session.user.organizationId!
      },
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        organization: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            students: true,
            assignedTests: true
          }
        }
      }
    })

    return NextResponse.json({ class: classData }, { status: 201 })
  } catch (error) {
    console.error('Failed to create class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

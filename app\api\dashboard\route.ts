import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, handleApiError } from '@/lib/middleware'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    // Get dashboard data based on user role
    switch (user.role) {
      case 'ADMIN':
        return getAdminDashboard(user)
      case 'TEACHER':
        return getTeacherDashboard(user)
      case 'STUDENT':
        return getStudentDashboard(user)
      default:
        return NextResponse.json({ error: 'Invalid user role' }, { status: 400 })
    }
  } catch (error) {
    return handleApiError(error, 'GET /api/dashboard')
  }
}

async function getAdminDashboard(user: any) {
  const [
    totalUsers,
    totalTests,
    totalOrganizations,
    totalCompletions,
    recentActivity
  ] = await Promise.all([
    db.user.count({
      where: user.organizationId ? { organizationId: user.organizationId } : {}
    }),
    db.test.count({
      where: user.organizationId ? { organizationId: user.organizationId } : {}
    }),
    db.organization.count(),
    db.testResult.count({
      where: user.organizationId ? {
        user: { organizationId: user.organizationId }
      } : {}
    }),
    db.testResult.findMany({
      where: user.organizationId ? {
        user: { organizationId: user.organizationId }
      } : {},
      include: {
        user: { select: { name: true } },
        test: { select: { title: true } }
      },
      orderBy: { submittedAt: 'desc' },
      take: 10
    })
  ])

  return NextResponse.json({
    stats: {
      totalUsers,
      totalTests,
      totalOrganizations,
      totalCompletions
    },
    recentActivity: recentActivity.map(activity => ({
      id: activity.id,
      type: 'test_completed',
      title: `${activity.user.name} completed "${activity.test.title}"`,
      description: `Score: ${activity.score} (${activity.percentage}%)`,
      timestamp: activity.submittedAt,
      status: activity.passed ? 'success' : 'error',
      metadata: {
        score: activity.score,
        percentage: activity.percentage,
        passed: activity.passed
      }
    }))
  })
}

async function getTeacherDashboard(user: any) {
  const [
    myClasses,
    totalStudents,
    myTests,
    pendingReviews,
    completions,
    averageScore,
    recentActivity
  ] = await Promise.all([
    db.class.count({
      where: { teacherId: user.id }
    }),
    db.classStudent.count({
      where: {
        class: { teacherId: user.id },
        status: 'ACTIVE'
      }
    }),
    db.test.count({
      where: { creatorId: user.id }
    }),
    db.testResult.count({
      where: {
        test: { creatorId: user.id },
        status: 'PENDING_REVIEW'
      }
    }),
    db.testResult.count({
      where: {
        test: { creatorId: user.id },
        submittedAt: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      }
    }),
    db.testResult.aggregate({
      where: {
        test: { creatorId: user.id }
      },
      _avg: { percentage: true }
    }),
    db.testResult.findMany({
      where: {
        test: { creatorId: user.id }
      },
      include: {
        user: { select: { name: true } },
        test: { select: { title: true } }
      },
      orderBy: { submittedAt: 'desc' },
      take: 10
    })
  ])

  return NextResponse.json({
    stats: {
      myClasses,
      totalStudents,
      myTests,
      pendingReviews,
      completions,
      averageScore: Math.round(averageScore._avg.percentage || 0)
    },
    recentActivity: recentActivity.map(activity => ({
      id: activity.id,
      type: 'test_completed',
      title: `${activity.user.name} completed "${activity.test.title}"`,
      description: `Score: ${activity.score} (${activity.percentage}%)`,
      timestamp: activity.submittedAt,
      status: activity.passed ? 'success' : 'error',
      metadata: {
        score: activity.score,
        percentage: activity.percentage,
        passed: activity.passed
      }
    }))
  })
}

async function getStudentDashboard(user: any) {
  const [
    userProfile,
    completedTests,
    averageScore,
    totalTimeSpent,
    recentResults,
    upcomingTests
  ] = await Promise.all([
    db.userProfile.findUnique({
      where: { userId: user.id }
    }),
    db.testResult.count({
      where: { userId: user.id }
    }),
    db.testResult.aggregate({
      where: { userId: user.id },
      _avg: { percentage: true }
    }),
    db.testResult.aggregate({
      where: { userId: user.id },
      _sum: { timeSpent: true }
    }),
    db.testResult.findMany({
      where: { userId: user.id },
      include: {
        test: { select: { title: true, type: true } }
      },
      orderBy: { submittedAt: 'desc' },
      take: 5
    }),
    db.testAssignment.findMany({
      where: {
        class: {
          students: {
            some: {
              studentId: user.id,
              status: 'ACTIVE'
            }
          }
        },
        OR: [
          { dueDate: { gte: new Date() } },
          { dueDate: null }
        ]
      },
      include: {
        test: { select: { id: true, title: true, type: true } }
      },
      orderBy: { dueDate: 'asc' },
      take: 5
    })
  ])

  return NextResponse.json({
    stats: {
      currentLevel: userProfile?.currentLevel || 'A1',
      completedTests,
      averageScore: Math.round(averageScore._avg.percentage || 0),
      timeSpent: totalTimeSpent._sum.timeSpent || 0
    },
    recentActivity: recentResults.map(result => ({
      id: result.id,
      type: 'test_completed',
      title: `Completed "${result.test.title}"`,
      description: `Score: ${result.score} (${result.percentage}%)`,
      timestamp: result.submittedAt,
      status: result.passed ? 'success' : 'error',
      metadata: {
        score: result.score,
        percentage: result.percentage,
        passed: result.passed
      }
    })),
    upcomingTests: upcomingTests.map(assignment => ({
      id: assignment.test.id,
      title: assignment.test.title,
      type: assignment.test.type,
      dueDate: assignment.dueDate
    }))
  })
}

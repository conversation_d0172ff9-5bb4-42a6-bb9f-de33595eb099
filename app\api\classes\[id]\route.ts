import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const classId = params.id

    const classData = await db.class.findUnique({
      where: { id: classId },
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        organization: {
          select: {
            id: true,
            name: true
          }
        },
        students: {
          include: {
            student: {
              select: {
                id: true,
                name: true,
                email: true,
                profile: {
                  select: {
                    currentLevel: true
                  }
                }
              }
            }
          },
          where: {
            status: 'ACTIVE'
          }
        },
        assignedTests: {
          include: {
            test: {
              select: {
                id: true,
                title: true,
                type: true,
                duration: true
              }
            }
          }
        }
      }
    })

    if (!classData) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    // Check permissions
    const canView = 
      session.user.role === 'ADMIN' ||
      classData.teacherId === session.user.id ||
      (session.user.role === 'STUDENT' && 
       classData.students.some(s => s.studentId === session.user.id))

    if (!canView) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    return NextResponse.json({ class: classData })
  } catch (error) {
    console.error('Failed to fetch class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const classId = params.id
    const body = await request.json()

    // Check if user can edit this class
    const existingClass = await db.class.findUnique({
      where: { id: classId },
      select: {
        teacherId: true,
        organizationId: true
      }
    })

    if (!existingClass) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    const canEdit = 
      session.user.role === 'ADMIN' ||
      existingClass.teacherId === session.user.id

    if (!canEdit) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const {
      name,
      description,
      level,
      startDate,
      endDate,
      maxStudents
    } = body

    // Validate dates if provided
    if (startDate && endDate) {
      const start = new Date(startDate)
      const end = new Date(endDate)
      
      if (end <= start) {
        return NextResponse.json(
          { error: 'End date must be after start date' },
          { status: 400 }
        )
      }
    }

    const updatedClass = await db.class.update({
      where: { id: classId },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(level && { level }),
        ...(startDate && { startDate: new Date(startDate) }),
        ...(endDate !== undefined && { endDate: endDate ? new Date(endDate) : null }),
        ...(maxStudents && { maxStudents })
      },
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        organization: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            students: true,
            assignedTests: true
          }
        }
      }
    })

    return NextResponse.json({ class: updatedClass })
  } catch (error) {
    console.error('Failed to update class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const classId = params.id

    // Check if user can delete this class
    const existingClass = await db.class.findUnique({
      where: { id: classId },
      select: {
        teacherId: true,
        organizationId: true,
        _count: {
          select: {
            students: true,
            assignedTests: true
          }
        }
      }
    })

    if (!existingClass) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    const canDelete = 
      session.user.role === 'ADMIN' ||
      existingClass.teacherId === session.user.id

    if (!canDelete) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if class has students or tests
    if (existingClass._count.students > 0 || existingClass._count.assignedTests > 0) {
      return NextResponse.json(
        { error: 'Cannot delete class with enrolled students or assigned tests' },
        { status: 400 }
      )
    }

    await db.class.delete({
      where: { id: classId }
    })

    return NextResponse.json({ message: 'Class deleted successfully' })
  } catch (error) {
    console.error('Failed to delete class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  Volume2, 
  Play, 
  Pause, 
  RotateCcw,
  Plus,
  Trash2,
  Headphones
} from 'lucide-react'

interface ListeningBuilderProps {
  audioUrl?: string
  options: any
  correctAnswer: any
  onAudioUrlChange: (url: string) => void
  onOptionsChange: (options: any) => void
  onCorrectAnswerChange: (correctAnswer: any) => void
}

export function ListeningBuilder({
  audioUrl,
  options,
  correctAnswer,
  onAudioUrlChange,
  onOptionsChange,
  onCorrectAnswerChange
}: ListeningBuilderProps) {
  const [questionType, setQuestionType] = useState('multiple_choice')
  const [transcript, setTranscript] = useState('')
  const [showTranscript, setShowTranscript] = useState(false)
  const [allowReplay, setAllowReplay] = useState(true)
  const [maxReplays, setMaxReplays] = useState(3)
  const [choices, setChoices] = useState<string[]>(['', ''])
  const [correctIndex, setCorrectIndex] = useState<number>(-1)
  const [fillInAnswer, setFillInAnswer] = useState('')

  useEffect(() => {
    if (options) {
      setQuestionType(options.questionType || 'multiple_choice')
      setTranscript(options.transcript || '')
      setShowTranscript(options.showTranscript || false)
      setAllowReplay(options.allowReplay !== false)
      setMaxReplays(options.maxReplays || 3)
      if (options.choices) {
        setChoices(options.choices)
      }
    }
    if (correctAnswer) {
      if (correctAnswer.correct !== undefined) {
        setCorrectIndex(correctAnswer.correct)
      }
      if (correctAnswer.answer) {
        setFillInAnswer(correctAnswer.answer)
      }
    }
  }, [options, correctAnswer])

  useEffect(() => {
    onOptionsChange({
      questionType,
      transcript: transcript.trim() || undefined,
      showTranscript,
      allowReplay,
      maxReplays: allowReplay ? maxReplays : undefined,
      choices: questionType === 'multiple_choice' ? choices.filter(c => c.trim()) : undefined
    })
  }, [questionType, transcript, showTranscript, allowReplay, maxReplays, choices, onOptionsChange])

  useEffect(() => {
    if (questionType === 'multiple_choice') {
      onCorrectAnswerChange({
        correct: correctIndex,
        answer: choices[correctIndex] || ''
      })
    } else {
      onCorrectAnswerChange({
        answer: fillInAnswer
      })
    }
  }, [questionType, correctIndex, choices, fillInAnswer, onCorrectAnswerChange])

  const addChoice = () => {
    if (choices.length < 6) {
      setChoices([...choices, ''])
    }
  }

  const removeChoice = (index: number) => {
    if (choices.length > 2) {
      const newChoices = choices.filter((_, i) => i !== index)
      setChoices(newChoices)
      
      if (correctIndex === index) {
        setCorrectIndex(-1)
      } else if (correctIndex > index) {
        setCorrectIndex(correctIndex - 1)
      }
    }
  }

  const updateChoice = (index: number, value: string) => {
    const newChoices = [...choices]
    newChoices[index] = value
    setChoices(newChoices)
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // In a real implementation, you would upload the file to a server
      // For now, we'll just create a mock URL
      const mockUrl = `audio/${file.name}`
      onAudioUrlChange(mockUrl)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Listening Comprehension Setup</CardTitle>
        <CardDescription>
          Configure audio content and question format for listening comprehension
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Audio Upload */}
        <div className="space-y-3">
          <Label>Audio File</Label>
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
            {audioUrl ? (
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Volume2 className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium">Audio uploaded: {audioUrl}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Play className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Replace
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center">
                <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm font-medium mb-1">Upload Audio File</p>
                <p className="text-xs text-muted-foreground mb-3">
                  Supported formats: MP3, WAV, M4A (max 50MB)
                </p>
                <input
                  type="file"
                  accept="audio/*"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="audio-upload"
                />
                <Button variant="outline" asChild>
                  <label htmlFor="audio-upload" className="cursor-pointer">
                    <Upload className="h-4 w-4 mr-2" />
                    Choose File
                  </label>
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Audio Settings */}
        <div className="space-y-4">
          <Label className="text-base font-medium">Audio Settings</Label>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="allowReplay">Allow Replay</Label>
              <p className="text-sm text-muted-foreground">
                Let students replay the audio
              </p>
            </div>
            <Switch
              id="allowReplay"
              checked={allowReplay}
              onCheckedChange={setAllowReplay}
            />
          </div>

          {allowReplay && (
            <div className="ml-6">
              <div className="space-y-2">
                <Label htmlFor="maxReplays">Maximum Replays</Label>
                <Select value={maxReplays.toString()} onValueChange={(value) => setMaxReplays(parseInt(value))}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 time</SelectItem>
                    <SelectItem value="2">2 times</SelectItem>
                    <SelectItem value="3">3 times</SelectItem>
                    <SelectItem value="5">5 times</SelectItem>
                    <SelectItem value="-1">Unlimited</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </div>

        {/* Question Type */}
        <div className="space-y-3">
          <Label>Question Type</Label>
          <Select value={questionType} onValueChange={setQuestionType}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
              <SelectItem value="fill_in_blank">Fill in the Blank</SelectItem>
              <SelectItem value="short_answer">Short Answer</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Question Type Specific Content */}
        {questionType === 'multiple_choice' && (
          <div className="space-y-3">
            <Label>Answer Choices</Label>
            <RadioGroup
              value={correctIndex.toString()}
              onValueChange={(value) => setCorrectIndex(parseInt(value))}
            >
              {choices.map((choice, index) => (
                <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                  <RadioGroupItem value={index.toString()} id={`listening-choice-${index}`} />
                  <Input
                    value={choice}
                    onChange={(e) => updateChoice(index, e.target.value)}
                    placeholder={`Option ${String.fromCharCode(65 + index)}`}
                    className="flex-1"
                  />
                  <span className="text-xs text-muted-foreground font-medium min-w-[20px]">
                    {String.fromCharCode(65 + index)}
                  </span>
                  {choices.length > 2 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeChoice(index)}
                      className="text-destructive hover:text-destructive h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </RadioGroup>

            {choices.length < 6 && (
              <Button
                type="button"
                variant="outline"
                onClick={addChoice}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Choice ({choices.length + 1}/6)
              </Button>
            )}
          </div>
        )}

        {(questionType === 'fill_in_blank' || questionType === 'short_answer') && (
          <div className="space-y-2">
            <Label htmlFor="fillInAnswer">Correct Answer</Label>
            <Input
              id="fillInAnswer"
              value={fillInAnswer}
              onChange={(e) => setFillInAnswer(e.target.value)}
              placeholder="Enter the correct answer..."
            />
          </div>
        )}

        {/* Transcript */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="showTranscript">Show Transcript</Label>
              <p className="text-sm text-muted-foreground">
                Provide transcript for accessibility
              </p>
            </div>
            <Switch
              id="showTranscript"
              checked={showTranscript}
              onCheckedChange={setShowTranscript}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="transcript">Audio Transcript</Label>
            <Textarea
              id="transcript"
              value={transcript}
              onChange={(e) => setTranscript(e.target.value)}
              placeholder="Enter the full transcript of the audio..."
              rows={4}
            />
            <p className="text-sm text-muted-foreground">
              {showTranscript 
                ? 'Students will see this transcript during the test'
                : 'Transcript will be hidden from students (for accessibility only)'
              }
            </p>
          </div>
        </div>

        {/* Preview */}
        <div className="pt-4 border-t">
          <Label className="text-sm font-medium">Preview</Label>
          <div className="mt-2 p-4 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium mb-3">Student will see:</p>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 border rounded bg-white">
                <Headphones className="h-5 w-5 text-blue-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Audio Player</p>
                  <p className="text-xs text-muted-foreground">
                    {allowReplay 
                      ? `Can replay ${maxReplays === -1 ? 'unlimited' : maxReplays} times`
                      : 'Single play only'
                    }
                  </p>
                </div>
                <div className="flex gap-1">
                  <Button variant="outline" size="sm">
                    <Play className="h-4 w-4" />
                  </Button>
                  {allowReplay && (
                    <Button variant="outline" size="sm">
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {questionType === 'multiple_choice' && choices.filter(c => c.trim()).length > 0 && (
                <div className="space-y-2">
                  {choices.filter(c => c.trim()).map((choice, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
                      <span className="text-sm">{String.fromCharCode(65 + index)}. {choice}</span>
                    </div>
                  ))}
                </div>
              )}

              {(questionType === 'fill_in_blank' || questionType === 'short_answer') && (
                <div className="border rounded p-2 bg-white">
                  <Input placeholder="Student answer input..." disabled />
                </div>
              )}

              {showTranscript && transcript && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                  <p className="text-xs font-medium text-blue-800 mb-1">Transcript:</p>
                  <p className="text-sm text-blue-700">{transcript}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

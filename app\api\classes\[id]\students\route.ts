import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const classId = params.id

    // Check if user can view this class
    const classData = await db.class.findUnique({
      where: { id: classId },
      select: {
        teacherId: true,
        organizationId: true
      }
    })

    if (!classData) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    const canView = 
      session.user.role === 'ADMIN' ||
      classData.teacherId === session.user.id

    if (!canView) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const students = await db.classStudent.findMany({
      where: { classId },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            email: true,
            profile: {
              select: {
                currentLevel: true,
                targetLevel: true
              }
            },
            testResults: {
              where: {
                test: {
                  assignments: {
                    some: {
                      classId
                    }
                  }
                }
              },
              select: {
                id: true,
                score: true,
                percentage: true,
                passed: true,
                submittedAt: true,
                test: {
                  select: {
                    id: true,
                    title: true
                  }
                }
              },
              orderBy: {
                submittedAt: 'desc'
              }
            }
          }
        }
      },
      orderBy: {
        enrolledAt: 'asc'
      }
    })

    return NextResponse.json({ students })
  } catch (error) {
    console.error('Failed to fetch class students:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const classId = params.id
    const body = await request.json()
    const { studentIds } = body

    if (!Array.isArray(studentIds) || studentIds.length === 0) {
      return NextResponse.json(
        { error: 'Student IDs array is required' },
        { status: 400 }
      )
    }

    // Check if user can manage this class
    const classData = await db.class.findUnique({
      where: { id: classId },
      select: {
        teacherId: true,
        maxStudents: true,
        _count: {
          select: {
            students: true
          }
        }
      }
    })

    if (!classData) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    const canManage = 
      session.user.role === 'ADMIN' ||
      classData.teacherId === session.user.id

    if (!canManage) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if adding students would exceed max capacity
    const currentStudents = classData._count.students
    if (currentStudents + studentIds.length > classData.maxStudents) {
      return NextResponse.json(
        { error: `Adding ${studentIds.length} students would exceed class capacity of ${classData.maxStudents}` },
        { status: 400 }
      )
    }

    // Verify all student IDs exist and are students
    const students = await db.user.findMany({
      where: {
        id: { in: studentIds },
        role: 'STUDENT'
      },
      select: { id: true }
    })

    if (students.length !== studentIds.length) {
      return NextResponse.json(
        { error: 'One or more invalid student IDs' },
        { status: 400 }
      )
    }

    // Check for already enrolled students
    const existingEnrollments = await db.classStudent.findMany({
      where: {
        classId,
        studentId: { in: studentIds }
      },
      select: { studentId: true }
    })

    const alreadyEnrolled = existingEnrollments.map(e => e.studentId)
    const newStudentIds = studentIds.filter(id => !alreadyEnrolled.includes(id))

    if (newStudentIds.length === 0) {
      return NextResponse.json(
        { error: 'All students are already enrolled in this class' },
        { status: 400 }
      )
    }

    // Enroll new students
    const enrollments = newStudentIds.map(studentId => ({
      classId,
      studentId,
      status: 'ACTIVE' as const
    }))

    await db.classStudent.createMany({
      data: enrollments
    })

    // Return updated student list
    const updatedStudents = await db.classStudent.findMany({
      where: { classId },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            email: true,
            profile: {
              select: {
                currentLevel: true,
                targetLevel: true
              }
            }
          }
        }
      },
      orderBy: {
        enrolledAt: 'asc'
      }
    })

    return NextResponse.json({ 
      message: `Successfully enrolled ${newStudentIds.length} students`,
      students: updatedStudents,
      skipped: alreadyEnrolled.length
    })
  } catch (error) {
    console.error('Failed to enroll students:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const classId = params.id
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')

    if (!studentId) {
      return NextResponse.json(
        { error: 'Student ID is required' },
        { status: 400 }
      )
    }

    // Check if user can manage this class
    const classData = await db.class.findUnique({
      where: { id: classId },
      select: {
        teacherId: true
      }
    })

    if (!classData) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    const canManage = 
      session.user.role === 'ADMIN' ||
      classData.teacherId === session.user.id

    if (!canManage) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Remove student from class
    const deleted = await db.classStudent.deleteMany({
      where: {
        classId,
        studentId
      }
    })

    if (deleted.count === 0) {
      return NextResponse.json(
        { error: 'Student not found in this class' },
        { status: 404 }
      )
    }

    return NextResponse.json({ 
      message: 'Student removed from class successfully' 
    })
  } catch (error) {
    console.error('Failed to remove student from class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

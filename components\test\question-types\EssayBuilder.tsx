'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { FileText, Clock, Target } from 'lucide-react'

interface EssayBuilderProps {
  options: any
  onOptionsChange: (options: any) => void
  isShortAnswer?: boolean
}

export function EssayBuilder({
  options,
  onOptionsChange,
  isShortAnswer = false
}: EssayBuilderProps) {
  const [minWords, setMinWords] = useState<number>(isShortAnswer ? 10 : 100)
  const [maxWords, setMaxWords] = useState<number>(isShortAnswer ? 100 : 500)
  const [enableWordCount, setEnableWordCount] = useState(true)
  const [enableTimeLimit, setEnableTimeLimit] = useState(false)
  const [timeLimit, setTimeLimit] = useState<number>(isShortAnswer ? 5 : 30)
  const [rubric, setRubric] = useState('')
  const [sampleAnswer, setSampleAnswer] = useState('')
  const [gradingCriteria, setGradingCriteria] = useState<string[]>([
    'Content and Ideas',
    'Organization and Structure',
    'Language and Grammar',
    'Vocabulary and Style'
  ])

  useEffect(() => {
    if (options) {
      setMinWords(options.minWords || (isShortAnswer ? 10 : 100))
      setMaxWords(options.maxWords || (isShortAnswer ? 100 : 500))
      setEnableWordCount(options.enableWordCount !== false)
      setEnableTimeLimit(options.enableTimeLimit || false)
      setTimeLimit(options.timeLimit || (isShortAnswer ? 5 : 30))
      setRubric(options.rubric || '')
      setSampleAnswer(options.sampleAnswer || '')
      if (options.gradingCriteria) {
        setGradingCriteria(options.gradingCriteria)
      }
    }
  }, [options, isShortAnswer])

  useEffect(() => {
    onOptionsChange({
      minWords: enableWordCount ? minWords : undefined,
      maxWords: enableWordCount ? maxWords : undefined,
      enableWordCount,
      enableTimeLimit,
      timeLimit: enableTimeLimit ? timeLimit : undefined,
      rubric: rubric.trim() || undefined,
      sampleAnswer: sampleAnswer.trim() || undefined,
      gradingCriteria,
      type: isShortAnswer ? 'short_answer' : 'essay'
    })
  }, [
    minWords,
    maxWords,
    enableWordCount,
    enableTimeLimit,
    timeLimit,
    rubric,
    sampleAnswer,
    gradingCriteria,
    isShortAnswer,
    onOptionsChange
  ])

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {isShortAnswer ? 'Short Answer' : 'Essay'} Configuration
        </CardTitle>
        <CardDescription>
          Set up requirements and grading criteria for written responses
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Word Count Settings */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="enableWordCount">Word Count Requirements</Label>
              <p className="text-sm text-muted-foreground">
                Set minimum and maximum word limits
              </p>
            </div>
            <Switch
              id="enableWordCount"
              checked={enableWordCount}
              onCheckedChange={setEnableWordCount}
            />
          </div>

          {enableWordCount && (
            <div className="grid grid-cols-2 gap-4 ml-6">
              <div className="space-y-2">
                <Label htmlFor="minWords">Minimum Words</Label>
                <Input
                  id="minWords"
                  type="number"
                  value={minWords}
                  onChange={(e) => setMinWords(parseInt(e.target.value) || 0)}
                  min="1"
                  max="10000"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxWords">Maximum Words</Label>
                <Input
                  id="maxWords"
                  type="number"
                  value={maxWords}
                  onChange={(e) => setMaxWords(parseInt(e.target.value) || 0)}
                  min="1"
                  max="10000"
                />
              </div>
            </div>
          )}
        </div>

        {/* Time Limit Settings */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="enableTimeLimit">Time Limit</Label>
              <p className="text-sm text-muted-foreground">
                Set a specific time limit for this question
              </p>
            </div>
            <Switch
              id="enableTimeLimit"
              checked={enableTimeLimit}
              onCheckedChange={setEnableTimeLimit}
            />
          </div>

          {enableTimeLimit && (
            <div className="ml-6">
              <div className="space-y-2">
                <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                <Input
                  id="timeLimit"
                  type="number"
                  value={timeLimit}
                  onChange={(e) => setTimeLimit(parseInt(e.target.value) || 0)}
                  min="1"
                  max="180"
                />
              </div>
            </div>
          )}
        </div>

        {/* Grading Rubric */}
        <div className="space-y-2">
          <Label htmlFor="rubric">Grading Rubric (Optional)</Label>
          <Textarea
            id="rubric"
            value={rubric}
            onChange={(e) => setRubric(e.target.value)}
            placeholder="Describe the grading criteria and expectations for this question..."
            rows={4}
          />
          <p className="text-sm text-muted-foreground">
            This will help teachers grade consistently and provide clear expectations to students.
          </p>
        </div>

        {/* Sample Answer */}
        <div className="space-y-2">
          <Label htmlFor="sampleAnswer">Sample Answer (Optional)</Label>
          <Textarea
            id="sampleAnswer"
            value={sampleAnswer}
            onChange={(e) => setSampleAnswer(e.target.value)}
            placeholder="Provide a sample answer or key points that should be included..."
            rows={isShortAnswer ? 3 : 6}
          />
          <p className="text-sm text-muted-foreground">
            This will help teachers understand what constitutes a good answer.
          </p>
        </div>

        {/* Grading Criteria */}
        {!isShortAnswer && (
          <div className="space-y-3">
            <Label>Grading Criteria</Label>
            <p className="text-sm text-muted-foreground">
              Key areas that will be evaluated in student responses
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {gradingCriteria.map((criteria, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-muted/50 rounded">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{criteria}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Preview */}
        <div className="pt-4 border-t">
          <Label className="text-sm font-medium">Preview</Label>
          <div className="mt-2 p-4 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium mb-3">Student will see:</p>
            <div className="space-y-3">
              <div className="border rounded p-3 bg-white min-h-[100px] flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <FileText className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">
                    {isShortAnswer ? 'Short answer text area' : 'Essay writing area'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                {enableWordCount && (
                  <div className="flex items-center gap-1">
                    <FileText className="h-3 w-3" />
                    <span>{minWords}-{maxWords} words</span>
                  </div>
                )}
                {enableTimeLimit && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{timeLimit} minutes</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Configuration Summary */}
        <div className="pt-4 border-t">
          <Label className="text-sm font-medium">Configuration Summary</Label>
          <div className="mt-2 space-y-1 text-sm text-muted-foreground">
            <p>• Type: {isShortAnswer ? 'Short Answer' : 'Essay'}</p>
            {enableWordCount && (
              <p>• Word limit: {minWords} - {maxWords} words</p>
            )}
            {enableTimeLimit && (
              <p>• Time limit: {timeLimit} minutes</p>
            )}
            <p>• Manual grading required</p>
            {rubric && <p>• Custom rubric provided</p>}
            {sampleAnswer && <p>• Sample answer provided</p>}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

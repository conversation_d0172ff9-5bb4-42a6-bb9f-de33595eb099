"use client"

import { useState, useEffect, useCallback } from "react"
import { QuestionType } from "@prisma/client"
import { ChevronLeft, ChevronRight, Flag, CheckCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Timer } from "./Timer"
import { ProgressBar } from "./ProgressBar"
import { QuestionRenderer } from "./QuestionRenderer"
import { ConfirmModal } from "@/components/ui/modal"

interface Question {
  id: string
  type: QuestionType
  content: string
  options?: any
  points: number
  audioUrl?: string
  imageUrl?: string
}

interface TestData {
  id: string
  title: string
  duration: number // in seconds
  questions: Question[]
}

interface TestInterfaceProps {
  test: TestData
  onSubmit: (answers: Record<string, any>, timeSpent: number) => void
  onSave?: (answers: Record<string, any>) => void
  autoSave?: boolean
  className?: string
}

export function TestInterface({
  test,
  onSubmit,
  onSave,
  autoSave = true,
  className
}: TestInterfaceProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<string, any>>({})
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<string>>(new Set())
  const [timeSpent, setTimeSpent] = useState(0)
  const [showSubmitModal, setShowSubmitModal] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const currentQuestion = test.questions[currentQuestionIndex]
  const totalQuestions = test.questions.length
  const answeredQuestions = Object.keys(answers).length
  const progress = (currentQuestionIndex + 1) / totalQuestions * 100

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && onSave && Object.keys(answers).length > 0) {
      const saveTimer = setTimeout(() => {
        onSave(answers)
      }, 2000) // Save after 2 seconds of inactivity

      return () => clearTimeout(saveTimer)
    }
  }, [answers, autoSave, onSave])

  const handleAnswerChange = useCallback((questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }))
  }, [])

  const handleTimeUpdate = useCallback((time: number) => {
    setTimeSpent(test.duration - time)
  }, [test.duration])

  const handleTimeUp = useCallback(() => {
    handleSubmit()
  }, [])

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1)
    }
  }

  const handleNextQuestion = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1)
    }
  }

  const handleQuestionNavigation = (index: number) => {
    setCurrentQuestionIndex(index)
  }

  const toggleFlag = (questionId: string) => {
    setFlaggedQuestions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(questionId)) {
        newSet.delete(questionId)
      } else {
        newSet.add(questionId)
      }
      return newSet
    })
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      await onSubmit(answers, timeSpent)
    } finally {
      setIsSubmitting(false)
      setShowSubmitModal(false)
    }
  }

  const getQuestionStatus = (questionId: string) => {
    if (answers[questionId] !== undefined) return "answered"
    if (flaggedQuestions.has(questionId)) return "flagged"
    return "unanswered"
  }

  return (
    <div className={cn("max-w-6xl mx-auto p-6", className)}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">{test.title}</h1>
          <Timer
            duration={test.duration}
            onTimeUpdate={handleTimeUpdate}
            onTimeUp={handleTimeUp}
          />
        </div>
        
        <ProgressBar
          current={currentQuestionIndex + 1}
          total={totalQuestions}
          showNumbers={true}
          className="mb-4"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Question Navigation Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Questions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-5 lg:grid-cols-4 gap-2">
                {test.questions.map((question, index) => {
                  const status = getQuestionStatus(question.id)
                  const isCurrent = index === currentQuestionIndex
                  
                  return (
                    <button
                      key={question.id}
                      onClick={() => handleQuestionNavigation(index)}
                      className={cn(
                        "w-10 h-10 rounded-lg text-sm font-medium transition-colors",
                        isCurrent && "ring-2 ring-blue-500",
                        status === "answered" && "bg-green-100 text-green-700",
                        status === "flagged" && "bg-yellow-100 text-yellow-700",
                        status === "unanswered" && "bg-gray-100 text-gray-700",
                        "hover:bg-opacity-80"
                      )}
                    >
                      {index + 1}
                      {flaggedQuestions.has(question.id) && (
                        <Flag className="w-3 h-3 absolute -mt-1 -mr-1" />
                      )}
                    </button>
                  )
                })}
              </div>
              
              <div className="mt-4 space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span>Answered:</span>
                  <span className="font-medium">{answeredQuestions}/{totalQuestions}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Flagged:</span>
                  <span className="font-medium">{flaggedQuestions.size}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Question Area */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>
                  Question {currentQuestionIndex + 1} of {totalQuestions}
                  <span className="ml-2 text-sm font-normal text-gray-500">
                    ({currentQuestion.points} {currentQuestion.points === 1 ? 'point' : 'points'})
                  </span>
                </CardTitle>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => toggleFlag(currentQuestion.id)}
                  className={cn(
                    flaggedQuestions.has(currentQuestion.id) && "bg-yellow-100 text-yellow-700"
                  )}
                >
                  <Flag className="w-4 h-4 mr-1" />
                  {flaggedQuestions.has(currentQuestion.id) ? "Unflag" : "Flag"}
                </Button>
              </div>
            </CardHeader>
            
            <CardContent>
              <QuestionRenderer
                question={currentQuestion}
                answer={answers[currentQuestion.id]}
                onAnswerChange={(answer) => handleAnswerChange(currentQuestion.id, answer)}
              />
            </CardContent>
          </Card>

          {/* Navigation Controls */}
          <div className="flex items-center justify-between mt-6">
            <Button
              variant="outline"
              onClick={handlePreviousQuestion}
              disabled={currentQuestionIndex === 0}
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              Previous
            </Button>

            <div className="flex space-x-2">
              {currentQuestionIndex === totalQuestions - 1 ? (
                <Button
                  onClick={() => setShowSubmitModal(true)}
                  className="bg-green-600 hover:bg-green-700"
                  disabled={isSubmitting}
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Submit Test
                </Button>
              ) : (
                <Button
                  onClick={handleNextQuestion}
                  disabled={currentQuestionIndex === totalQuestions - 1}
                >
                  Next
                  <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Submit Confirmation Modal */}
      <ConfirmModal
        isOpen={showSubmitModal}
        onClose={() => setShowSubmitModal(false)}
        onConfirm={handleSubmit}
        title="Submit Test"
        message={`Are you sure you want to submit your test? You have answered ${answeredQuestions} out of ${totalQuestions} questions.`}
        confirmText="Submit"
        cancelText="Continue Test"
      />
    </div>
  )
}

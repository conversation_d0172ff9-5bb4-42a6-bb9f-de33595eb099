{"name": "test-system", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "repository": {"type": "git", "url": "git+https://github.com/MrFarrukhT/test-system.git"}, "keywords": ["english", "testing", "ielts", "education", "nextjs"], "author": "Mr<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/MrFarrukhT/test-system/issues"}, "homepage": "https://github.com/MrFarrukhT/test-system#readme", "description": "English Level Testing System with IELTS Support", "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@prisma/client": "^6.9.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "lucide-react": "^0.513.0", "next": "15.3", "next-auth": "^4.24.11", "postcss": "^8.5.4", "prisma": "^6.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3"}}
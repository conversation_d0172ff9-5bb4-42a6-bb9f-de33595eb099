'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Switch } from '@/components/ui/switch'
import { Plus, Trash2, GripVertical } from 'lucide-react'

interface MultipleChoiceBuilderProps {
  options: any
  correctAnswer: any
  onOptionsChange: (options: any) => void
  onCorrectAnswerChange: (correctAnswer: any) => void
  error?: string
}

export function MultipleChoiceBuilder({
  options,
  correctAnswer,
  onOptionsChange,
  onCorrectAnswerChange,
  error
}: MultipleChoiceBuilderProps) {
  const [choices, setChoices] = useState<string[]>(['', ''])
  const [correctIndex, setCorrectIndex] = useState<number>(-1)
  const [allowMultiple, setAllowMultiple] = useState(false)
  const [shuffleOptions, setShuffleOptions] = useState(false)

  useEffect(() => {
    if (options?.choices) {
      setChoices(options.choices)
      setAllowMultiple(options.allowMultiple || false)
      setShuffleOptions(options.shuffleOptions || false)
    }
    if (correctAnswer?.correct !== undefined) {
      setCorrectIndex(correctAnswer.correct)
    }
  }, [options, correctAnswer])

  useEffect(() => {
    onOptionsChange({
      choices: choices.filter(choice => choice.trim() !== ''),
      allowMultiple,
      shuffleOptions
    })
  }, [choices, allowMultiple, shuffleOptions, onOptionsChange])

  useEffect(() => {
    onCorrectAnswerChange({
      correct: correctIndex,
      answer: choices[correctIndex] || ''
    })
  }, [correctIndex, choices, onCorrectAnswerChange])

  const addChoice = () => {
    if (choices.length < 6) {
      setChoices([...choices, ''])
    }
  }

  const removeChoice = (index: number) => {
    if (choices.length > 2) {
      const newChoices = choices.filter((_, i) => i !== index)
      setChoices(newChoices)
      
      // Adjust correct answer index if necessary
      if (correctIndex === index) {
        setCorrectIndex(-1)
      } else if (correctIndex > index) {
        setCorrectIndex(correctIndex - 1)
      }
    }
  }

  const updateChoice = (index: number, value: string) => {
    const newChoices = [...choices]
    newChoices[index] = value
    setChoices(newChoices)
  }

  const moveChoice = (fromIndex: number, toIndex: number) => {
    const newChoices = [...choices]
    const [movedChoice] = newChoices.splice(fromIndex, 1)
    newChoices.splice(toIndex, 0, movedChoice)
    setChoices(newChoices)

    // Adjust correct answer index
    if (correctIndex === fromIndex) {
      setCorrectIndex(toIndex)
    } else if (correctIndex > fromIndex && correctIndex <= toIndex) {
      setCorrectIndex(correctIndex - 1)
    } else if (correctIndex < fromIndex && correctIndex >= toIndex) {
      setCorrectIndex(correctIndex + 1)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Multiple Choice Options</CardTitle>
        <CardDescription>
          Create answer choices and select the correct one
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        {/* Options */}
        <div className="space-y-3">
          <Label>Answer Choices</Label>
          <RadioGroup
            value={correctIndex.toString()}
            onValueChange={(value) => setCorrectIndex(parseInt(value))}
          >
            {choices.map((choice, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="flex items-center justify-center w-6 h-6 text-muted-foreground cursor-grab">
                  <GripVertical className="h-4 w-4" />
                </div>
                
                <RadioGroupItem value={index.toString()} id={`choice-${index}`} />
                
                <div className="flex-1">
                  <Input
                    value={choice}
                    onChange={(e) => updateChoice(index, e.target.value)}
                    placeholder={`Option ${String.fromCharCode(65 + index)}`}
                    className="border-0 shadow-none focus-visible:ring-0 p-0"
                  />
                </div>

                <div className="flex items-center gap-1">
                  <span className="text-xs text-muted-foreground font-medium">
                    {String.fromCharCode(65 + index)}
                  </span>
                  {choices.length > 2 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeChoice(index)}
                      className="text-destructive hover:text-destructive h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </RadioGroup>

          {choices.length < 6 && (
            <Button
              type="button"
              variant="outline"
              onClick={addChoice}
              className="w-full"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Choice ({choices.length + 1}/6)
            </Button>
          )}
        </div>

        {/* Correct Answer Indicator */}
        {correctIndex >= 0 && choices[correctIndex] && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="text-sm font-medium text-green-800">
              Correct Answer: {String.fromCharCode(65 + correctIndex)} - {choices[correctIndex]}
            </div>
          </div>
        )}

        {/* Advanced Options */}
        <div className="space-y-4 pt-4 border-t">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="allowMultiple">Allow Multiple Selections</Label>
              <p className="text-sm text-muted-foreground">
                Students can select more than one answer
              </p>
            </div>
            <Switch
              id="allowMultiple"
              checked={allowMultiple}
              onCheckedChange={setAllowMultiple}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="shuffleOptions">Shuffle Options</Label>
              <p className="text-sm text-muted-foreground">
                Randomize the order of choices for each student
              </p>
            </div>
            <Switch
              id="shuffleOptions"
              checked={shuffleOptions}
              onCheckedChange={setShuffleOptions}
            />
          </div>
        </div>

        {/* Preview */}
        <div className="pt-4 border-t">
          <Label className="text-sm font-medium">Preview</Label>
          <div className="mt-2 p-4 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium mb-3">Sample Question Preview:</p>
            <div className="space-y-2">
              {choices.filter(choice => choice.trim()).map((choice, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                    index === correctIndex ? 'border-green-500 bg-green-500' : 'border-gray-300'
                  }`}>
                    {index === correctIndex && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <span className="text-sm">
                    {String.fromCharCode(65 + index)}. {choice}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

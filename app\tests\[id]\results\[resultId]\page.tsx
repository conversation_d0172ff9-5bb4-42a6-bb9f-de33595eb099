"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useSession } from "next-auth/react"
import { LoadingPage } from "@/components/ui/loading"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CircularProgress } from "@/components/test/ProgressBar"
import { QuestionRenderer } from "@/components/test/QuestionRenderer"
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Trophy, 
  Target,
  BookOpen,
  ArrowLeft,
  Download
} from "lucide-react"
import { formatTime, formatDateTime } from "@/lib/utils"
import { cn } from "@/lib/utils"

interface TestResult {
  id: string
  attempt: number
  totalQuestions: number
  correctAnswers: number
  score: number
  percentage: number
  passed: boolean
  timeSpent: number
  startedAt: string
  completedAt: string
  // IELTS specific
  listeningScore?: number
  readingScore?: number
  writingScore?: number
  speakingScore?: number
  overallBandScore?: number
  test: {
    id: string
    title: string
    type: string
    passingScore: number
    bandScoring: boolean
  }
  user: {
    id: string
    name: string
    email: string
  }
  answers: Array<{
    id: string
    answer: any
    isCorrect: boolean
    pointsEarned: number
    timeSpent?: number
    question: {
      id: string
      content: string
      type: string
      options?: any
      correctAnswer: any
      explanation?: string
      points: number
    }
  }>
}

export default function TestResultPage({ 
  params 
}: { 
  params: { id: string; resultId: string } 
}) {
  const router = useRouter()
  const { data: session, status } = useSession()
  
  const [result, setResult] = useState<TestResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [showAnswers, setShowAnswers] = useState(false)

  useEffect(() => {
    if (status === "authenticated") {
      fetchResult()
    }
  }, [status, params.resultId])

  const fetchResult = async () => {
    try {
      const response = await fetch(`/api/tests/${params.id}/results`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          resultId: params.resultId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch result')
      }

      const data = await response.json()
      setResult(data.result)
    } catch (err) {
      console.error('Failed to fetch result:', err)
      router.push(`/tests/${params.id}`)
    } finally {
      setLoading(false)
    }
  }

  const handleDownloadCertificate = async () => {
    // TODO: Implement certificate generation
    console.log('Download certificate')
  }

  if (status === "loading" || loading) {
    return <LoadingPage />
  }

  if (status === "unauthenticated") {
    router.push('/auth/signin')
    return null
  }

  if (!result) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <h2 className="text-xl font-semibold mb-2">Result Not Found</h2>
            <p className="text-gray-600 mb-4">The test result you're looking for doesn't exist.</p>
            <Button onClick={() => router.push('/tests')}>
              Back to Tests
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const isIELTS = result.test.type === 'IELTS_MOCK' && result.test.bandScoring

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.push(`/tests/${params.id}`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Test
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{result.test.title}</h1>
            <p className="text-gray-600">Test Result - Attempt {result.attempt}</p>
          </div>
        </div>
        
        {result.passed && (
          <Button onClick={handleDownloadCertificate} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download Certificate
          </Button>
        )}
      </div>

      {/* Result Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Overall Score */}
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center space-x-2">
              {result.passed ? (
                <CheckCircle className="h-6 w-6 text-green-500" />
              ) : (
                <XCircle className="h-6 w-6 text-red-500" />
              )}
              <span>{result.passed ? 'Passed' : 'Failed'}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <CircularProgress
              percentage={result.percentage}
              size={120}
              color={result.passed ? "#10b981" : "#ef4444"}
              className="mb-4"
            />
            <div className="space-y-2">
              <p className="text-2xl font-bold">{result.score} points</p>
              <p className="text-gray-600">
                {result.correctAnswers} of {result.totalQuestions} correct
              </p>
              <Badge variant={result.passed ? "default" : "destructive"}>
                {result.percentage}% (Required: {result.test.passingScore}%)
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* IELTS Scores */}
        {isIELTS && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Trophy className="h-5 w-5" />
                <span>IELTS Band Scores</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {result.listeningScore && (
                  <div className="flex justify-between">
                    <span>Listening:</span>
                    <Badge variant="outline">{result.listeningScore}</Badge>
                  </div>
                )}
                {result.readingScore && (
                  <div className="flex justify-between">
                    <span>Reading:</span>
                    <Badge variant="outline">{result.readingScore}</Badge>
                  </div>
                )}
                {result.writingScore && (
                  <div className="flex justify-between">
                    <span>Writing:</span>
                    <Badge variant="outline">{result.writingScore}</Badge>
                  </div>
                )}
                {result.speakingScore && (
                  <div className="flex justify-between">
                    <span>Speaking:</span>
                    <Badge variant="outline">{result.speakingScore}</Badge>
                  </div>
                )}
                {result.overallBandScore && (
                  <div className="flex justify-between pt-2 border-t">
                    <span className="font-semibold">Overall:</span>
                    <Badge className="bg-blue-600">{result.overallBandScore}</Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Test Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Test Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>Time Spent:</span>
              <span className="font-medium">{formatTime(result.timeSpent)}</span>
            </div>
            <div className="flex justify-between">
              <span>Started:</span>
              <span className="font-medium">{formatDateTime(new Date(result.startedAt))}</span>
            </div>
            <div className="flex justify-between">
              <span>Completed:</span>
              <span className="font-medium">{formatDateTime(new Date(result.completedAt))}</span>
            </div>
            <div className="flex justify-between">
              <span>Test Type:</span>
              <Badge variant="outline">{result.test.type.replace('_', ' ')}</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Answer Review */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5" />
              <span>Answer Review</span>
            </CardTitle>
            <Button
              variant="outline"
              onClick={() => setShowAnswers(!showAnswers)}
            >
              {showAnswers ? 'Hide' : 'Show'} Answers
            </Button>
          </div>
        </CardHeader>
        
        {showAnswers && (
          <CardContent>
            <div className="space-y-6">
              {result.answers.map((answer, index) => (
                <div
                  key={answer.id}
                  className={cn(
                    "p-4 rounded-lg border",
                    answer.isCorrect ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
                  )}
                >
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="font-medium">
                      Question {index + 1}
                      <span className="ml-2 text-sm text-gray-500">
                        ({answer.question.points} {answer.question.points === 1 ? 'point' : 'points'})
                      </span>
                    </h3>
                    <div className="flex items-center space-x-2">
                      {answer.isCorrect ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                      <span className="text-sm font-medium">
                        {answer.pointsEarned} / {answer.question.points}
                      </span>
                    </div>
                  </div>

                  <QuestionRenderer
                    question={answer.question}
                    answer={answer.answer}
                    onAnswerChange={() => {}} // Read-only
                    disabled={true}
                    showCorrectAnswer={true}
                    correctAnswer={answer.question.correctAnswer}
                  />

                  {answer.question.explanation && (
                    <div className="mt-4 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                      <p className="text-sm text-blue-800">
                        <strong>Explanation:</strong> {answer.question.explanation}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}

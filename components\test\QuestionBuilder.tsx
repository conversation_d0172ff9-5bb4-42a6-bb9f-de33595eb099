'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { MultipleChoiceBuilder } from '@/components/test/question-types/MultipleChoiceBuilder'
import { FillInBlankBuilder } from '@/components/test/question-types/FillInBlankBuilder'
import { EssayBuilder } from '@/components/test/question-types/EssayBuilder'
import { TrueFalseBuilder } from '@/components/test/question-types/TrueFalseBuilder'
import { ListeningBuilder } from '@/components/test/question-types/ListeningBuilder'
import { ReadingBuilder } from '@/components/test/question-types/ReadingBuilder'
import { 
  X, 
  Save, 
  Eye, 
  Upload,
  Plus,
  Trash2
} from 'lucide-react'

interface Question {
  id?: string
  type: string
  content: string
  order: number
  points: number
  difficulty: string
  tags: string[]
  options?: any
  correctAnswer?: any
  explanation?: string
  audioUrl?: string
  imageUrl?: string
  estimatedTime?: number
}

interface QuestionBuilderProps {
  sectionId: string
  testType: string
  editingQuestion?: Question | null
  onSave: () => void
  onCancel: () => void
}

export function QuestionBuilder({ 
  sectionId, 
  testType, 
  editingQuestion, 
  onSave, 
  onCancel 
}: QuestionBuilderProps) {
  const [formData, setFormData] = useState<Question>({
    type: 'MULTIPLE_CHOICE',
    content: '',
    order: 1,
    points: 1,
    difficulty: 'MEDIUM',
    tags: [],
    options: {},
    correctAnswer: {},
    explanation: '',
    estimatedTime: 60
  })
  const [newTag, setNewTag] = useState('')
  const [loading, setSaving] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (editingQuestion) {
      setFormData(editingQuestion)
    }
  }, [editingQuestion])

  const questionTypes = [
    { value: 'MULTIPLE_CHOICE', label: 'Multiple Choice', description: 'Choose from multiple options' },
    { value: 'TRUE_FALSE', label: 'True/False', description: 'Simple true or false question' },
    { value: 'FILL_IN_BLANK', label: 'Fill in the Blank', description: 'Complete missing words or phrases' },
    { value: 'ESSAY', label: 'Essay', description: 'Long-form written response' },
    { value: 'SHORT_ANSWER', label: 'Short Answer', description: 'Brief written response' },
    { value: 'LISTENING_COMPREHENSION', label: 'Listening', description: 'Audio-based question' },
    { value: 'READING_COMPREHENSION', label: 'Reading', description: 'Text passage-based question' }
  ]

  const difficulties = [
    { value: 'EASY', label: 'Easy', color: 'bg-green-100 text-green-800' },
    { value: 'MEDIUM', label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'HARD', label: 'Hard', color: 'bg-red-100 text-red-800' }
  ]

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleOptionsChange = (options: any) => {
    setFormData(prev => ({ ...prev, options }))
  }

  const handleCorrectAnswerChange = (correctAnswer: any) => {
    setFormData(prev => ({ ...prev, correctAnswer }))
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.content.trim()) {
      newErrors.content = 'Question content is required'
    }

    if (formData.points <= 0) {
      newErrors.points = 'Points must be greater than 0'
    }

    if (formData.estimatedTime && formData.estimatedTime <= 0) {
      newErrors.estimatedTime = 'Estimated time must be greater than 0'
    }

    // Type-specific validation
    if (formData.type === 'MULTIPLE_CHOICE') {
      if (!formData.options?.choices || formData.options.choices.length < 2) {
        newErrors.options = 'Multiple choice questions need at least 2 options'
      }
      if (formData.options?.correct === undefined) {
        newErrors.correctAnswer = 'Please select the correct answer'
      }
    }

    if (formData.type === 'FILL_IN_BLANK') {
      if (!formData.correctAnswer?.answer) {
        newErrors.correctAnswer = 'Please provide the correct answer'
      }
    }

    if (formData.type === 'TRUE_FALSE') {
      if (formData.correctAnswer?.answer === undefined) {
        newErrors.correctAnswer = 'Please select true or false'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setSaving(true)

    try {
      const url = editingQuestion 
        ? `/api/questions/${editingQuestion.id}`
        : '/api/questions'
      
      const method = editingQuestion ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          sectionId: editingQuestion ? undefined : sectionId
        }),
      })

      if (response.ok) {
        onSave()
      } else {
        const error = await response.json()
        setErrors({ submit: error.error || 'Failed to save question' })
      }
    } catch (error) {
      setErrors({ submit: 'An error occurred while saving the question' })
    } finally {
      setSaving(false)
    }
  }

  const renderQuestionTypeBuilder = () => {
    switch (formData.type) {
      case 'MULTIPLE_CHOICE':
        return (
          <MultipleChoiceBuilder
            options={formData.options}
            correctAnswer={formData.correctAnswer}
            onOptionsChange={handleOptionsChange}
            onCorrectAnswerChange={handleCorrectAnswerChange}
            error={errors.options || errors.correctAnswer}
          />
        )
      case 'TRUE_FALSE':
        return (
          <TrueFalseBuilder
            correctAnswer={formData.correctAnswer}
            onCorrectAnswerChange={handleCorrectAnswerChange}
            error={errors.correctAnswer}
          />
        )
      case 'FILL_IN_BLANK':
        return (
          <FillInBlankBuilder
            content={formData.content}
            correctAnswer={formData.correctAnswer}
            options={formData.options}
            onContentChange={(content) => handleChange('content', content)}
            onCorrectAnswerChange={handleCorrectAnswerChange}
            onOptionsChange={handleOptionsChange}
            error={errors.correctAnswer}
          />
        )
      case 'ESSAY':
      case 'SHORT_ANSWER':
        return (
          <EssayBuilder
            options={formData.options}
            onOptionsChange={handleOptionsChange}
            isShortAnswer={formData.type === 'SHORT_ANSWER'}
          />
        )
      case 'LISTENING_COMPREHENSION':
        return (
          <ListeningBuilder
            audioUrl={formData.audioUrl}
            options={formData.options}
            correctAnswer={formData.correctAnswer}
            onAudioUrlChange={(url) => handleChange('audioUrl', url)}
            onOptionsChange={handleOptionsChange}
            onCorrectAnswerChange={handleCorrectAnswerChange}
          />
        )
      case 'READING_COMPREHENSION':
        return (
          <ReadingBuilder
            options={formData.options}
            correctAnswer={formData.correctAnswer}
            onOptionsChange={handleOptionsChange}
            onCorrectAnswerChange={handleCorrectAnswerChange}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-background rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold">
              {editingQuestion ? 'Edit Question' : 'Add New Question'}
            </h2>
            <p className="text-muted-foreground">
              Create engaging questions for your test
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => setShowPreview(!showPreview)}
            >
              <Eye className="mr-2 h-4 w-4" />
              {showPreview ? 'Hide' : 'Show'} Preview
            </Button>
            <Button variant="outline" onClick={onCancel}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {errors.submit && (
              <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
                {errors.submit}
              </div>
            )}

            {/* Question Type Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Question Type</CardTitle>
                <CardDescription>
                  Choose the type of question you want to create
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Select 
                  value={formData.type} 
                  onValueChange={(value) => handleChange('type', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {questionTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div>
                          <div className="font-medium">{type.label}</div>
                          <div className="text-sm text-muted-foreground">
                            {type.description}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </CardContent>
            </Card>

            {/* Basic Question Information */}
            <Card>
              <CardHeader>
                <CardTitle>Question Content</CardTitle>
                <CardDescription>
                  Enter the main question text and basic settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="content">Question Text *</Label>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) => handleChange('content', e.target.value)}
                    placeholder="Enter your question here..."
                    rows={3}
                    className={errors.content ? 'border-destructive' : ''}
                  />
                  {errors.content && (
                    <p className="text-sm text-destructive">{errors.content}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="points">Points *</Label>
                    <Input
                      id="points"
                      type="number"
                      value={formData.points}
                      onChange={(e) => handleChange('points', parseFloat(e.target.value))}
                      min="0.1"
                      step="0.1"
                      className={errors.points ? 'border-destructive' : ''}
                    />
                    {errors.points && (
                      <p className="text-sm text-destructive">{errors.points}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="difficulty">Difficulty</Label>
                    <Select 
                      value={formData.difficulty} 
                      onValueChange={(value) => handleChange('difficulty', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {difficulties.map((diff) => (
                          <SelectItem key={diff.value} value={diff.value}>
                            <Badge className={diff.color}>
                              {diff.label}
                            </Badge>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="estimatedTime">Est. Time (seconds)</Label>
                    <Input
                      id="estimatedTime"
                      type="number"
                      value={formData.estimatedTime || ''}
                      onChange={(e) => handleChange('estimatedTime', parseInt(e.target.value))}
                      min="1"
                      placeholder="60"
                      className={errors.estimatedTime ? 'border-destructive' : ''}
                    />
                    {errors.estimatedTime && (
                      <p className="text-sm text-destructive">{errors.estimatedTime}</p>
                    )}
                  </div>
                </div>

                {/* Tags */}
                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {formData.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="flex items-center gap-1">
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-1 hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag..."
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" variant="outline" onClick={addTag}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Question Type Specific Builder */}
            {renderQuestionTypeBuilder()}

            {/* Explanation */}
            <Card>
              <CardHeader>
                <CardTitle>Explanation (Optional)</CardTitle>
                <CardDescription>
                  Provide an explanation that will be shown after the question is answered
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={formData.explanation || ''}
                  onChange={(e) => handleChange('explanation', e.target.value)}
                  placeholder="Explain why this is the correct answer..."
                  rows={3}
                />
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="flex justify-end gap-4 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {editingQuestion ? 'Update Question' : 'Save Question'}
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

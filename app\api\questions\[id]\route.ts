import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const question = await db.question.findUnique({
      where: { id: params.id },
      include: {
        section: {
          include: {
            test: {
              include: {
                creator: {
                  select: { id: true, name: true }
                }
              }
            }
          }
        }
      }
    })

    if (!question) {
      return NextResponse.json({ error: 'Question not found' }, { status: 404 })
    }

    // Check access permissions
    const test = question.section.test
    const canAccess = 
      test.isPublic ||
      session.user.role === 'ADMIN' ||
      test.creator.id === session.user.id

    if (!canAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    return NextResponse.json({ question })
  } catch (error) {
    console.error('Failed to fetch question:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers and admins can edit questions
    if (session.user.role === 'STUDENT') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const question = await db.question.findUnique({
      where: { id: params.id },
      include: {
        section: {
          include: {
            test: {
              include: {
                creator: {
                  select: { id: true }
                }
              }
            }
          }
        }
      }
    })

    if (!question) {
      return NextResponse.json({ error: 'Question not found' }, { status: 404 })
    }

    // Check edit permissions
    const canEdit = 
      session.user.role === 'ADMIN' ||
      question.section.test.creator.id === session.user.id

    if (!canEdit) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const body = await request.json()
    const {
      type,
      content,
      points,
      difficulty,
      tags,
      options,
      correctAnswer,
      explanation,
      audioUrl,
      imageUrl,
      estimatedTime
    } = body

    const updatedQuestion = await db.question.update({
      where: { id: params.id },
      data: {
        type,
        content,
        points,
        difficulty,
        tags,
        options,
        correctAnswer,
        explanation,
        audioUrl,
        imageUrl,
        estimatedTime
      }
    })

    return NextResponse.json({ question: updatedQuestion })
  } catch (error) {
    console.error('Failed to update question:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers and admins can delete questions
    if (session.user.role === 'STUDENT') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const question = await db.question.findUnique({
      where: { id: params.id },
      include: {
        section: {
          include: {
            test: {
              include: {
                creator: {
                  select: { id: true }
                }
              }
            }
          }
        }
      }
    })

    if (!question) {
      return NextResponse.json({ error: 'Question not found' }, { status: 404 })
    }

    // Check delete permissions
    const canDelete = 
      session.user.role === 'ADMIN' ||
      question.section.test.creator.id === session.user.id

    if (!canDelete) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Delete the question
    await db.question.delete({
      where: { id: params.id }
    })

    // Reorder remaining questions in the section
    const remainingQuestions = await db.question.findMany({
      where: { sectionId: question.sectionId },
      orderBy: { order: 'asc' }
    })

    // Update order for remaining questions
    for (let i = 0; i < remainingQuestions.length; i++) {
      await db.question.update({
        where: { id: remainingQuestions[i].id },
        data: { order: i + 1 }
      })
    }

    return NextResponse.json({ message: 'Question deleted successfully' })
  } catch (error) {
    console.error('Failed to delete question:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

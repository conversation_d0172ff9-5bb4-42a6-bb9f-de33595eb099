import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, handleApiError } from '@/lib/middleware'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const classId = params.id

    // Check if user can view this class
    const classData = await db.class.findUnique({
      where: { id: classId },
      select: {
        id: true,
        name: true,
        teacherId: true,
        level: true,
        startDate: true,
        endDate: true,
        maxStudents: true
      }
    })

    if (!classData) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    const canView = 
      user.role === 'ADMIN' ||
      classData.teacherId === user.id

    if (!canView) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get class statistics
    const [
      studentsCount,
      activeStudents,
      assignedTests,
      completedTests,
      averageScores,
      recentActivity
    ] = await Promise.all([
      // Total students
      db.classStudent.count({
        where: { classId }
      }),

      // Active students
      db.classStudent.count({
        where: { 
          classId,
          status: 'ACTIVE'
        }
      }),

      // Assigned tests
      db.testAssignment.findMany({
        where: { classId },
        include: {
          test: {
            select: {
              id: true,
              title: true,
              type: true,
              duration: true,
              passingScore: true
            }
          }
        }
      }),

      // Completed test results
      db.testResult.findMany({
        where: {
          test: {
            assignments: {
              some: { classId }
            }
          },
          user: {
            classStudents: {
              some: {
                classId,
                status: 'ACTIVE'
              }
            }
          }
        },
        include: {
          test: {
            select: {
              id: true,
              title: true,
              type: true
            }
          },
          user: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }),

      // Average scores by test
      db.testResult.groupBy({
        by: ['testId'],
        where: {
          test: {
            assignments: {
              some: { classId }
            }
          },
          user: {
            classStudents: {
              some: {
                classId,
                status: 'ACTIVE'
              }
            }
          }
        },
        _avg: {
          score: true,
          percentage: true
        },
        _count: {
          id: true
        }
      }),

      // Recent activity (last 30 days)
      db.testResult.findMany({
        where: {
          test: {
            assignments: {
              some: { classId }
            }
          },
          user: {
            classStudents: {
              some: {
                classId,
                status: 'ACTIVE'
              }
            }
          },
          submittedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        include: {
          test: {
            select: {
              title: true
            }
          },
          user: {
            select: {
              name: true
            }
          }
        },
        orderBy: {
          submittedAt: 'desc'
        },
        take: 20
      })
    ])

    // Calculate test completion rates
    const testCompletionRates = assignedTests.map(assignment => {
      const testResults = completedTests.filter(result => result.test.id === assignment.testId)
      const completionRate = activeStudents > 0 ? (testResults.length / activeStudents) * 100 : 0
      const averageScore = averageScores.find(avg => avg.testId === assignment.testId)

      return {
        testId: assignment.testId,
        testTitle: assignment.test.title,
        testType: assignment.test.type,
        assignedAt: assignment.assignedAt,
        dueDate: assignment.dueDate,
        completionRate: Math.round(completionRate),
        completedCount: testResults.length,
        totalStudents: activeStudents,
        averageScore: averageScore?._avg.score || 0,
        averagePercentage: averageScore?._avg.percentage || 0,
        passRate: testResults.length > 0 
          ? Math.round((testResults.filter(r => r.passed).length / testResults.length) * 100)
          : 0
      }
    })

    // Calculate overall class performance
    const overallStats = {
      totalStudents: studentsCount,
      activeStudents,
      totalTests: assignedTests.length,
      completedTests: completedTests.length,
      averageClassScore: completedTests.length > 0
        ? Math.round(completedTests.reduce((sum, result) => sum + result.score, 0) / completedTests.length)
        : 0,
      averageClassPercentage: completedTests.length > 0
        ? Math.round(completedTests.reduce((sum, result) => sum + result.percentage, 0) / completedTests.length)
        : 0,
      overallPassRate: completedTests.length > 0
        ? Math.round((completedTests.filter(r => r.passed).length / completedTests.length) * 100)
        : 0
    }

    // Student performance breakdown
    const studentPerformance = await db.classStudent.findMany({
      where: { 
        classId,
        status: 'ACTIVE'
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            email: true,
            profile: {
              select: {
                currentLevel: true,
                targetLevel: true
              }
            }
          }
        }
      }
    })

    const studentsWithStats = await Promise.all(
      studentPerformance.map(async (enrollment) => {
        const studentResults = completedTests.filter(result => result.user.id === enrollment.student.id)
        
        return {
          studentId: enrollment.student.id,
          studentName: enrollment.student.name,
          studentEmail: enrollment.student.email,
          currentLevel: enrollment.student.profile?.currentLevel,
          targetLevel: enrollment.student.profile?.targetLevel,
          enrolledAt: enrollment.enrolledAt,
          testsCompleted: studentResults.length,
          totalTestsAssigned: assignedTests.length,
          averageScore: studentResults.length > 0
            ? Math.round(studentResults.reduce((sum, result) => sum + result.score, 0) / studentResults.length)
            : 0,
          averagePercentage: studentResults.length > 0
            ? Math.round(studentResults.reduce((sum, result) => sum + result.percentage, 0) / studentResults.length)
            : 0,
          passRate: studentResults.length > 0
            ? Math.round((studentResults.filter(r => r.passed).length / studentResults.length) * 100)
            : 0,
          lastActivity: studentResults.length > 0
            ? studentResults.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())[0].submittedAt
            : null
        }
      })
    )

    // Performance trends (last 7 days)
    const weeklyTrend = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      date.setHours(0, 0, 0, 0)
      
      const nextDate = new Date(date)
      nextDate.setDate(nextDate.getDate() + 1)

      const dayResults = completedTests.filter(result => {
        const submittedDate = new Date(result.submittedAt)
        return submittedDate >= date && submittedDate < nextDate
      })

      weeklyTrend.push({
        date: date.toISOString().split('T')[0],
        testsCompleted: dayResults.length,
        averageScore: dayResults.length > 0
          ? Math.round(dayResults.reduce((sum, result) => sum + result.score, 0) / dayResults.length)
          : 0
      })
    }

    return NextResponse.json({
      class: classData,
      overallStats,
      testCompletionRates,
      studentsWithStats,
      weeklyTrend,
      recentActivity: recentActivity.map(activity => ({
        id: activity.id,
        studentName: activity.user.name,
        testTitle: activity.test.title,
        score: activity.score,
        percentage: activity.percentage,
        passed: activity.passed,
        submittedAt: activity.submittedAt
      }))
    })

  } catch (error) {
    return handleApiError(error, 'GET /api/classes/[id]/analytics')
  }
}

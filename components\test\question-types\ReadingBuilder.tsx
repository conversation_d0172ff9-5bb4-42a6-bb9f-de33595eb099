'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Plus, 
  Trash2, 
  BookOpen,
  Eye,
  Clock
} from 'lucide-react'

interface ReadingBuilderProps {
  options: any
  correctAnswer: any
  onOptionsChange: (options: any) => void
  onCorrectAnswerChange: (correctAnswer: any) => void
}

export function ReadingBuilder({
  options,
  correctAnswer,
  onOptionsChange,
  onCorrectAnswerChange
}: ReadingBuilderProps) {
  const [passage, setPassage] = useState('')
  const [passageTitle, setPassageTitle] = useState('')
  const [questionType, setQuestionType] = useState('multiple_choice')
  const [choices, setChoices] = useState<string[]>(['', ''])
  const [correctIndex, setCorrectIndex] = useState<number>(-1)
  const [shortAnswer, setShortAnswer] = useState('')
  const [readingTime, setReadingTime] = useState<number>(5)
  const [wordCount, setWordCount] = useState(0)

  useEffect(() => {
    if (options) {
      setPassage(options.passage || '')
      setPassageTitle(options.passageTitle || '')
      setQuestionType(options.questionType || 'multiple_choice')
      setReadingTime(options.readingTime || 5)
      if (options.choices) {
        setChoices(options.choices)
      }
    }
    if (correctAnswer) {
      if (correctAnswer.correct !== undefined) {
        setCorrectIndex(correctAnswer.correct)
      }
      if (correctAnswer.answer) {
        setShortAnswer(correctAnswer.answer)
      }
    }
  }, [options, correctAnswer])

  useEffect(() => {
    const words = passage.trim().split(/\s+/).filter(word => word.length > 0)
    setWordCount(words.length)
  }, [passage])

  useEffect(() => {
    onOptionsChange({
      passage: passage.trim() || undefined,
      passageTitle: passageTitle.trim() || undefined,
      questionType,
      readingTime,
      wordCount,
      choices: questionType === 'multiple_choice' ? choices.filter(c => c.trim()) : undefined
    })
  }, [passage, passageTitle, questionType, readingTime, wordCount, choices, onOptionsChange])

  useEffect(() => {
    if (questionType === 'multiple_choice') {
      onCorrectAnswerChange({
        correct: correctIndex,
        answer: choices[correctIndex] || ''
      })
    } else {
      onCorrectAnswerChange({
        answer: shortAnswer
      })
    }
  }, [questionType, correctIndex, choices, shortAnswer, onCorrectAnswerChange])

  const addChoice = () => {
    if (choices.length < 6) {
      setChoices([...choices, ''])
    }
  }

  const removeChoice = (index: number) => {
    if (choices.length > 2) {
      const newChoices = choices.filter((_, i) => i !== index)
      setChoices(newChoices)
      
      if (correctIndex === index) {
        setCorrectIndex(-1)
      } else if (correctIndex > index) {
        setCorrectIndex(correctIndex - 1)
      }
    }
  }

  const updateChoice = (index: number, value: string) => {
    const newChoices = [...choices]
    newChoices[index] = value
    setChoices(newChoices)
  }

  const estimatedReadingTime = Math.ceil(wordCount / 200) // Average reading speed: 200 words per minute

  return (
    <Card>
      <CardHeader>
        <CardTitle>Reading Comprehension Setup</CardTitle>
        <CardDescription>
          Configure reading passage and comprehension questions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Passage Content */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="passageTitle">Passage Title (Optional)</Label>
            <Input
              id="passageTitle"
              value={passageTitle}
              onChange={(e) => setPassageTitle(e.target.value)}
              placeholder="Enter a title for the reading passage..."
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="passage">Reading Passage *</Label>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  {wordCount} words
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  ~{estimatedReadingTime} min read
                </span>
              </div>
            </div>
            <Textarea
              id="passage"
              value={passage}
              onChange={(e) => setPassage(e.target.value)}
              placeholder="Enter the reading passage here. This should be a complete text that students will read before answering questions..."
              rows={12}
              className="font-serif leading-relaxed"
            />
            <p className="text-sm text-muted-foreground">
              Recommended length: 200-800 words depending on student level and test duration.
            </p>
          </div>
        </div>

        {/* Reading Time */}
        <div className="space-y-2">
          <Label htmlFor="readingTime">Recommended Reading Time (minutes)</Label>
          <div className="flex items-center gap-4">
            <Input
              id="readingTime"
              type="number"
              value={readingTime}
              onChange={(e) => setReadingTime(parseInt(e.target.value) || 0)}
              min="1"
              max="30"
              className="w-32"
            />
            <span className="text-sm text-muted-foreground">
              Estimated: {estimatedReadingTime} minutes (based on {wordCount} words)
            </span>
          </div>
        </div>

        {/* Question Type */}
        <div className="space-y-3">
          <Label>Question Type</Label>
          <Select value={questionType} onValueChange={setQuestionType}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
              <SelectItem value="short_answer">Short Answer</SelectItem>
              <SelectItem value="true_false">True/False</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Question Type Specific Content */}
        {questionType === 'multiple_choice' && (
          <div className="space-y-3">
            <Label>Answer Choices</Label>
            <RadioGroup
              value={correctIndex.toString()}
              onValueChange={(value) => setCorrectIndex(parseInt(value))}
            >
              {choices.map((choice, index) => (
                <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                  <RadioGroupItem value={index.toString()} id={`reading-choice-${index}`} />
                  <Input
                    value={choice}
                    onChange={(e) => updateChoice(index, e.target.value)}
                    placeholder={`Option ${String.fromCharCode(65 + index)}`}
                    className="flex-1"
                  />
                  <span className="text-xs text-muted-foreground font-medium min-w-[20px]">
                    {String.fromCharCode(65 + index)}
                  </span>
                  {choices.length > 2 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeChoice(index)}
                      className="text-destructive hover:text-destructive h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </RadioGroup>

            {choices.length < 6 && (
              <Button
                type="button"
                variant="outline"
                onClick={addChoice}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Choice ({choices.length + 1}/6)
              </Button>
            )}

            {correctIndex >= 0 && choices[correctIndex] && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="text-sm font-medium text-green-800">
                  Correct Answer: {String.fromCharCode(65 + correctIndex)} - {choices[correctIndex]}
                </div>
              </div>
            )}
          </div>
        )}

        {(questionType === 'short_answer' || questionType === 'true_false') && (
          <div className="space-y-2">
            <Label htmlFor="shortAnswer">
              {questionType === 'true_false' ? 'Correct Answer (True/False)' : 'Correct Answer'}
            </Label>
            {questionType === 'true_false' ? (
              <Select value={shortAnswer} onValueChange={setShortAnswer}>
                <SelectTrigger>
                  <SelectValue placeholder="Select correct answer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Input
                id="shortAnswer"
                value={shortAnswer}
                onChange={(e) => setShortAnswer(e.target.value)}
                placeholder="Enter the correct answer..."
              />
            )}
          </div>
        )}

        {/* Passage Statistics */}
        <div className="pt-4 border-t">
          <Label className="text-sm font-medium">Passage Statistics</Label>
          <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center p-3 bg-muted/50 rounded">
              <div className="font-semibold">{wordCount}</div>
              <div className="text-muted-foreground">Words</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded">
              <div className="font-semibold">{passage.length}</div>
              <div className="text-muted-foreground">Characters</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded">
              <div className="font-semibold">{estimatedReadingTime}</div>
              <div className="text-muted-foreground">Est. Minutes</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded">
              <div className="font-semibold">{passage.split(/[.!?]+/).length - 1}</div>
              <div className="text-muted-foreground">Sentences</div>
            </div>
          </div>
        </div>

        {/* Preview */}
        <div className="pt-4 border-t">
          <Label className="text-sm font-medium">Preview</Label>
          <div className="mt-2 p-4 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium mb-3">Student will see:</p>
            <div className="space-y-4">
              {/* Passage Preview */}
              <div className="border rounded p-4 bg-white max-h-40 overflow-y-auto">
                <div className="flex items-center gap-2 mb-2">
                  <BookOpen className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">
                    {passageTitle || 'Reading Passage'}
                  </span>
                </div>
                <div className="text-sm font-serif leading-relaxed text-muted-foreground">
                  {passage ? (
                    passage.length > 200 ? passage.substring(0, 200) + '...' : passage
                  ) : (
                    'Passage content will appear here...'
                  )}
                </div>
              </div>

              {/* Question Preview */}
              {questionType === 'multiple_choice' && choices.filter(c => c.trim()).length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Question choices:</p>
                  {choices.filter(c => c.trim()).map((choice, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
                      <span className="text-sm">{String.fromCharCode(65 + index)}. {choice}</span>
                    </div>
                  ))}
                </div>
              )}

              {questionType === 'short_answer' && (
                <div className="border rounded p-2 bg-white">
                  <Input placeholder="Student answer input..." disabled />
                </div>
              )}

              {questionType === 'true_false' && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
                    <span className="text-sm">True</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
                    <span className="text-sm">False</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

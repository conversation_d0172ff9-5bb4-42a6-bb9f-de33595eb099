"use client"

import { useState, useEffect } from "react"
import { QuestionType } from "@prisma/client"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"

interface Question {
  id: string
  type: QuestionType
  content: string
  options?: any
  points: number
  audioUrl?: string
  imageUrl?: string
}

interface QuestionRendererProps {
  question: Question
  answer?: any
  onAnswerChange: (answer: any) => void
  disabled?: boolean
  showCorrectAnswer?: boolean
  correctAnswer?: any
  className?: string
}

export function QuestionRenderer({
  question,
  answer,
  onAnswerChange,
  disabled = false,
  showCorrectAnswer = false,
  correctAnswer,
  className
}: QuestionRendererProps) {
  const [localAnswer, setLocalAnswer] = useState(answer)

  useEffect(() => {
    setLocalAnswer(answer)
  }, [answer])

  const handleAnswerChange = (newAnswer: any) => {
    setLocalAnswer(newAnswer)
    onAnswerChange(newAnswer)
  }

  const renderQuestionContent = () => {
    return (
      <div className="space-y-4">
        {/* Audio player */}
        {question.audioUrl && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <audio controls className="w-full">
              <source src={question.audioUrl} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        )}

        {/* Image */}
        {question.imageUrl && (
          <div className="flex justify-center">
            <img 
              src={question.imageUrl} 
              alt="Question image" 
              className="max-w-full h-auto rounded-lg shadow-sm"
            />
          </div>
        )}

        {/* Question text */}
        <div 
          className="prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: question.content }}
        />
      </div>
    )
  }

  const renderAnswerInput = () => {
    switch (question.type) {
      case QuestionType.MULTIPLE_CHOICE:
        return (
          <MultipleChoiceInput
            options={question.options}
            value={localAnswer}
            onChange={handleAnswerChange}
            disabled={disabled}
            showCorrectAnswer={showCorrectAnswer}
            correctAnswer={correctAnswer}
          />
        )

      case QuestionType.TRUE_FALSE:
        return (
          <TrueFalseInput
            value={localAnswer}
            onChange={handleAnswerChange}
            disabled={disabled}
            showCorrectAnswer={showCorrectAnswer}
            correctAnswer={correctAnswer}
          />
        )

      case QuestionType.FILL_IN_BLANK:
        return (
          <FillInBlankInput
            value={localAnswer}
            onChange={handleAnswerChange}
            disabled={disabled}
            showCorrectAnswer={showCorrectAnswer}
            correctAnswer={correctAnswer}
          />
        )

      case QuestionType.SHORT_ANSWER:
        return (
          <ShortAnswerInput
            value={localAnswer}
            onChange={handleAnswerChange}
            disabled={disabled}
            showCorrectAnswer={showCorrectAnswer}
            correctAnswer={correctAnswer}
          />
        )

      case QuestionType.ESSAY:
        return (
          <EssayInput
            value={localAnswer}
            onChange={handleAnswerChange}
            disabled={disabled}
            showCorrectAnswer={showCorrectAnswer}
            correctAnswer={correctAnswer}
          />
        )

      default:
        return <div className="text-gray-500">Question type not supported</div>
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      {renderQuestionContent()}
      {renderAnswerInput()}
    </div>
  )
}

// Multiple Choice Input Component
function MultipleChoiceInput({ 
  options, 
  value, 
  onChange, 
  disabled, 
  showCorrectAnswer, 
  correctAnswer 
}: any) {
  const isMultipleSelect = options?.multiple || false

  if (isMultipleSelect) {
    return (
      <div className="space-y-2">
        {options?.choices?.map((option: any, index: number) => (
          <div key={index} className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={`option-${index}`}
              checked={value?.includes(option.value) || false}
              onChange={(e) => {
                const newValue = value || []
                if (e.target.checked) {
                  onChange([...newValue, option.value])
                } else {
                  onChange(newValue.filter((v: any) => v !== option.value))
                }
              }}
              disabled={disabled}
              className="rounded border-gray-300"
            />
            <Label 
              htmlFor={`option-${index}`}
              className={cn(
                showCorrectAnswer && correctAnswer?.includes(option.value) && "text-green-600 font-medium"
              )}
            >
              {option.text}
            </Label>
          </div>
        ))}
      </div>
    )
  }

  return (
    <RadioGroup value={value} onValueChange={onChange} disabled={disabled}>
      {options?.choices?.map((option: any, index: number) => (
        <div key={index} className="flex items-center space-x-2">
          <RadioGroupItem value={option.value} id={`option-${index}`} />
          <Label 
            htmlFor={`option-${index}`}
            className={cn(
              showCorrectAnswer && correctAnswer === option.value && "text-green-600 font-medium"
            )}
          >
            {option.text}
          </Label>
        </div>
      ))}
    </RadioGroup>
  )
}

// True/False Input Component
function TrueFalseInput({ value, onChange, disabled, showCorrectAnswer, correctAnswer }: any) {
  return (
    <RadioGroup value={value?.toString()} onValueChange={(v) => onChange(v === "true")} disabled={disabled}>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="true" id="true" />
        <Label 
          htmlFor="true"
          className={cn(
            showCorrectAnswer && correctAnswer === true && "text-green-600 font-medium"
          )}
        >
          True
        </Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="false" id="false" />
        <Label 
          htmlFor="false"
          className={cn(
            showCorrectAnswer && correctAnswer === false && "text-green-600 font-medium"
          )}
        >
          False
        </Label>
      </div>
    </RadioGroup>
  )
}

// Fill in the Blank Input Component
function FillInBlankInput({ value, onChange, disabled, showCorrectAnswer, correctAnswer }: any) {
  return (
    <div className="space-y-2">
      <Input
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        placeholder="Enter your answer..."
        className={cn(
          showCorrectAnswer && "border-green-500"
        )}
      />
      {showCorrectAnswer && correctAnswer && (
        <div className="text-sm text-green-600">
          Correct answer: {Array.isArray(correctAnswer) ? correctAnswer.join(", ") : correctAnswer}
        </div>
      )}
    </div>
  )
}

// Short Answer Input Component
function ShortAnswerInput({ value, onChange, disabled, showCorrectAnswer, correctAnswer }: any) {
  return (
    <div className="space-y-2">
      <Input
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        placeholder="Enter your short answer..."
        className={cn(
          showCorrectAnswer && "border-green-500"
        )}
      />
      {showCorrectAnswer && correctAnswer && (
        <div className="text-sm text-green-600">
          Sample answer: {Array.isArray(correctAnswer) ? correctAnswer.join(", ") : correctAnswer}
        </div>
      )}
    </div>
  )
}

// Essay Input Component
function EssayInput({ value, onChange, disabled, showCorrectAnswer, correctAnswer }: any) {
  const [wordCount, setWordCount] = useState(0)

  useEffect(() => {
    if (value) {
      const words = value.trim().split(/\s+/).filter((word: string) => word.length > 0)
      setWordCount(words.length)
    } else {
      setWordCount(0)
    }
  }, [value])

  return (
    <div className="space-y-2">
      <Textarea
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        placeholder="Write your essay here..."
        rows={8}
        className={cn(
          showCorrectAnswer && "border-green-500"
        )}
      />
      <div className="text-sm text-gray-500">
        Word count: {wordCount}
      </div>
      {showCorrectAnswer && correctAnswer && (
        <div className="text-sm text-green-600">
          Sample response available for review
        </div>
      )}
    </div>
  )
}

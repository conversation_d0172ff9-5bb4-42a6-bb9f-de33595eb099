import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, requireAdmin, validatePagination, validateSearch, handleApi<PERSON>rror, validateRequiredFields } from '@/lib/middleware'
import { db } from '@/lib/db'
import { OrganizationType } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const { searchParams } = new URL(request.url)
    const { page, limit, skip } = validatePagination(searchParams)
    const search = validateSearch(searchParams)
    const type = searchParams.get('type') as OrganizationType | null

    // Build where clause
    const where: any = {}

    // Role-based filtering
    if (user.role !== 'ADMIN') {
      // Non-admins can only see their own organization
      if (user.organizationId) {
        where.id = user.organizationId
      } else {
        // User has no organization, return empty result
        return NextResponse.json({
          organizations: [],
          pagination: {
            page: 1,
            limit,
            total: 0,
            totalPages: 0
          }
        })
      }
    }

    // Apply filters
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }
    if (type) {
      where.type = type
    }

    const [organizations, total] = await Promise.all([
      db.organization.findMany({
        where,
        include: {
          _count: {
            select: {
              users: true,
              classes: true,
              tests: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      db.organization.count({ where })
    ])

    return NextResponse.json({
      organizations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    return handleApiError(error, 'GET /api/organizations')
  }
}

export async function POST(request: NextRequest) {
  try {
    const { user, error } = await requireAdmin(request)
    if (error) return error

    const body = await request.json()
    const requiredFields = ['name', 'type']
    const missingFields = validateRequiredFields(body, requiredFields)

    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      )
    }

    const {
      name,
      type,
      description,
      website,
      email,
      phone,
      address,
      settings
    } = body

    // Validate type
    if (!Object.values(OrganizationType).includes(type)) {
      return NextResponse.json(
        { error: 'Invalid organization type' },
        { status: 400 }
      )
    }

    // Validate email format if provided
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return NextResponse.json(
          { error: 'Invalid email format' },
          { status: 400 }
        )
      }
    }

    // Check if organization name already exists
    const existingOrg = await db.organization.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive'
        }
      }
    })

    if (existingOrg) {
      return NextResponse.json(
        { error: 'Organization with this name already exists' },
        { status: 409 }
      )
    }

    const organization = await db.organization.create({
      data: {
        name,
        type,
        description,
        website,
        email,
        phone,
        address,
        settings: settings || {}
      },
      include: {
        _count: {
          select: {
            users: true,
            classes: true,
            tests: true
          }
        }
      }
    })

    return NextResponse.json({ organization }, { status: 201 })
  } catch (error) {
    return handleApiError(error, 'POST /api/organizations')
  }
}

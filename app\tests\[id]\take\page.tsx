"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { TestInterface } from "@/components/test/TestInterface"
import { LoadingPage } from "@/components/ui/loading"
import { useToast } from "@/components/ui/toast"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Clock, Users, BookOpen, AlertTriangle } from "lucide-react"
import { formatTime } from "@/lib/utils"

interface TestData {
  id: string
  title: string
  description?: string
  duration: number
  type: string
  level?: string
  maxAttempts: number
  passingScore: number
  questions: any[]
  creator: {
    name: string
  }
  _count: {
    results: number
  }
}

interface TestAttempt {
  attempt: number
  totalAttempts: number
  canTakeTest: boolean
  timeRemaining?: number
}

export default function TakeTestPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { data: session, status } = useSession()
  const { success, error } = useToast()
  
  const [test, setTest] = useState<TestData | null>(null)
  const [testAttempt, setTestAttempt] = useState<TestAttempt | null>(null)
  const [loading, setLoading] = useState(true)
  const [starting, setStarting] = useState(false)
  const [hasStarted, setHasStarted] = useState(false)

  useEffect(() => {
    if (status === "authenticated") {
      fetchTestData()
    }
  }, [status, params.id])

  const fetchTestData = async () => {
    try {
      const response = await fetch(`/api/tests/${params.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch test')
      }
      
      const data = await response.json()
      setTest(data.test)

      // Check user's attempt status
      const attemptResponse = await fetch(`/api/tests/${params.id}/attempt-status`)
      if (attemptResponse.ok) {
        const attemptData = await attemptResponse.json()
        setTestAttempt(attemptData)
      }
    } catch (err) {
      error('Failed to load test', 'Please try again later')
      router.push('/tests')
    } finally {
      setLoading(false)
    }
  }

  const handleStartTest = async () => {
    setStarting(true)
    try {
      const response = await fetch(`/api/tests/${params.id}/start`, {
        method: 'POST'
      })
      
      if (!response.ok) {
        throw new Error('Failed to start test')
      }
      
      setHasStarted(true)
      success('Test started', 'Good luck!')
    } catch (err) {
      error('Failed to start test', 'Please try again')
    } finally {
      setStarting(false)
    }
  }

  const handleSubmitTest = async (answers: Record<string, any>, timeSpent: number) => {
    try {
      const response = await fetch(`/api/tests/${params.id}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          answers,
          timeSpent
        })
      })

      if (!response.ok) {
        throw new Error('Failed to submit test')
      }

      const result = await response.json()
      success('Test submitted successfully!', 'Redirecting to results...')
      
      // Redirect to results page
      setTimeout(() => {
        router.push(`/tests/${params.id}/results/${result.result.id}`)
      }, 2000)
    } catch (err) {
      error('Failed to submit test', 'Please try again')
    }
  }

  const handleSaveProgress = async (answers: Record<string, any>) => {
    try {
      await fetch(`/api/tests/${params.id}/save-progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ answers })
      })
    } catch (err) {
      console.error('Failed to save progress:', err)
    }
  }

  if (status === "loading" || loading) {
    return <LoadingPage />
  }

  if (status === "unauthenticated") {
    router.push('/auth/signin')
    return null
  }

  if (!test) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Test Not Found</h2>
            <p className="text-gray-600 mb-4">The test you're looking for doesn't exist or you don't have permission to access it.</p>
            <Button onClick={() => router.push('/tests')}>
              Back to Tests
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (testAttempt && !testAttempt.canTakeTest) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Maximum Attempts Reached</h2>
            <p className="text-gray-600 mb-4">
              You have completed all {test.maxAttempts} allowed attempts for this test.
            </p>
            <Button onClick={() => router.push('/tests')}>
              Back to Tests
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!hasStarted) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">{test.title}</CardTitle>
            {test.description && (
              <p className="text-gray-600">{test.description}</p>
            )}
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Test Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-500">Duration</p>
                  <p className="font-medium">{formatTime(test.duration)}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <BookOpen className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm text-gray-500">Questions</p>
                  <p className="font-medium">{test.questions.length}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm text-gray-500">Attempts</p>
                  <p className="font-medium">
                    {testAttempt ? testAttempt.attempt : 1} of {test.maxAttempts}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-sm text-gray-500">Passing Score</p>
                  <p className="font-medium">{test.passingScore}%</p>
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Test Instructions</h3>
              <ul className="text-blue-800 space-y-1 text-sm">
                <li>• Read each question carefully before answering</li>
                <li>• You can navigate between questions using the navigation panel</li>
                <li>• Your progress will be saved automatically</li>
                <li>• Make sure you have a stable internet connection</li>
                <li>• Once you start, the timer will begin and cannot be paused</li>
                <li>• Submit your test before time runs out</li>
              </ul>
            </div>

            {/* Start Button */}
            <div className="text-center">
              <Button
                onClick={handleStartTest}
                disabled={starting}
                size="lg"
                className="px-8"
              >
                {starting ? 'Starting Test...' : 'Start Test'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <TestInterface
      test={test}
      onSubmit={handleSubmitTest}
      onSave={handleSaveProgress}
      autoSave={true}
    />
  )
}

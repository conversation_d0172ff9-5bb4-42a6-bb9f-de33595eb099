'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { QuestionBuilder } from '@/components/test/QuestionBuilder'
import { QuestionList } from '@/components/test/QuestionList'
import { 
  ArrowLeft, 
  Plus, 
  FileText, 
  Eye,
  Settings,
  Save
} from 'lucide-react'

interface Test {
  id: string
  title: string
  type: string
  level: string
  creator: {
    id: string
    name: string
  }
}

interface TestSection {
  id: string
  title: string
  description: string
  order: number
  duration?: number
  questions: Question[]
}

interface Question {
  id: string
  type: string
  content: string
  order: number
  points: number
  difficulty: string
  tags: string[]
  options?: any
  correctAnswer?: any
  explanation?: string
  audioUrl?: string
  imageUrl?: string
  estimatedTime?: number
}

export default function QuestionsPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const [test, setTest] = useState<Test | null>(null)
  const [sections, setSections] = useState<TestSection[]>([])
  const [selectedSection, setSelectedSection] = useState<string>('')
  const [showQuestionBuilder, setShowQuestionBuilder] = useState(false)
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchTestData()
    }
  }, [params.id])

  const fetchTestData = async () => {
    try {
      const [testResponse, sectionsResponse] = await Promise.all([
        fetch(`/api/tests/${params.id}`),
        fetch(`/api/tests/${params.id}/sections`)
      ])

      if (testResponse.ok && sectionsResponse.ok) {
        const testData = await testResponse.json()
        const sectionsData = await sectionsResponse.json()
        
        setTest(testData.test)
        setSections(sectionsData.sections)
        
        if (sectionsData.sections.length > 0 && !selectedSection) {
          setSelectedSection(sectionsData.sections[0].id)
        }
      }
    } catch (error) {
      console.error('Failed to fetch test data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleQuestionSaved = () => {
    fetchTestData()
    setShowQuestionBuilder(false)
    setEditingQuestion(null)
  }

  const handleEditQuestion = (question: Question) => {
    setEditingQuestion(question)
    setShowQuestionBuilder(true)
  }

  const handleDeleteQuestion = async (questionId: string) => {
    if (!confirm('Are you sure you want to delete this question?')) return

    try {
      const response = await fetch(`/api/questions/${questionId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        fetchTestData()
      }
    } catch (error) {
      console.error('Failed to delete question:', error)
    }
  }

  const canEdit = session?.user?.role === 'ADMIN' || 
                 (session?.user?.role === 'TEACHER' && test?.creator.id === session.user.id)

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="w-10 h-10 bg-gray-200 rounded animate-pulse"></div>
          <div className="space-y-2">
            <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!test || !canEdit) {
    router.push(`/tests/${params.id}`)
    return null
  }

  const currentSection = sections.find(s => s.id === selectedSection)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href={`/tests/${params.id}`}>
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold">Question Builder</h1>
          <p className="text-muted-foreground">
            {test.title} • Manage test questions
          </p>
        </div>
        <div className="flex gap-2">
          <Link href={`/tests/${params.id}/preview`}>
            <Button variant="outline">
              <Eye className="mr-2 h-4 w-4" />
              Preview Test
            </Button>
          </Link>
          <Button 
            onClick={() => setShowQuestionBuilder(true)}
            disabled={!selectedSection}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Question
          </Button>
        </div>
      </div>

      {sections.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No sections found</h3>
            <p className="text-muted-foreground text-center mb-4">
              You need to create test sections before adding questions.
            </p>
            <Link href={`/tests/${params.id}/sections/create`}>
              <Button>Create Section</Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Section Selector */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Test Sections</CardTitle>
                <CardDescription>
                  Select a section to manage questions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {sections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setSelectedSection(section.id)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors ${
                      selectedSection === section.id
                        ? 'bg-primary text-primary-foreground border-primary'
                        : 'hover:bg-accent border-border'
                    }`}
                  >
                    <div className="font-medium">{section.title}</div>
                    <div className="text-sm opacity-75">
                      {section.questions?.length || 0} questions
                    </div>
                  </button>
                ))}
                <Link href={`/tests/${params.id}/sections/create`}>
                  <Button variant="outline" size="sm" className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Section
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          {/* Questions List */}
          <div className="lg:col-span-3">
            {currentSection ? (
              <QuestionList
                section={currentSection}
                onEditQuestion={handleEditQuestion}
                onDeleteQuestion={handleDeleteQuestion}
                onAddQuestion={() => setShowQuestionBuilder(true)}
              />
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Settings className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Select a section</h3>
                  <p className="text-muted-foreground text-center">
                    Choose a section from the sidebar to view and manage questions.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* Question Builder Modal */}
      {showQuestionBuilder && currentSection && (
        <QuestionBuilder
          sectionId={selectedSection}
          testType={test.type}
          editingQuestion={editingQuestion}
          onSave={handleQuestionSaved}
          onCancel={() => {
            setShowQuestionBuilder(false)
            setEditingQuestion(null)
          }}
        />
      )}
    </div>
  )
}

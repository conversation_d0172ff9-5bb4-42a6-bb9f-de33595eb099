"use client"

import { IELTSType, TestType } from '@prisma/client'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Clock, 
  Users, 
  BookOpen, 
  Headphones, 
  PenTool, 
  Mic,
  Play,
  Eye,
  BarChart3,
  Calendar
} from 'lucide-react'
import { formatTime, formatDateTime } from '@/lib/utils'
import { cn } from '@/lib/utils'

interface IELTSTest {
  id: string
  title: string
  description?: string
  type: TestType
  ieltsType: IELTSType
  duration: number
  level?: string
  bandScoring: boolean
  isPublic: boolean
  createdAt: Date
  creator: {
    name: string
  }
  _count: {
    results: number
    sections: number
  }
  sections?: Array<{
    id: string
    title: string
    skill: string
    duration?: number
    _count: {
      questions: number
    }
  }>
}

interface IELTSTestCardProps {
  test: IELTSTest
  userRole?: 'ADMIN' | 'TEACHER' | 'STUDENT'
  onTakeTest?: (testId: string) => void
  onViewResults?: (testId: string) => void
  onViewDetails?: (testId: string) => void
  onEditTest?: (testId: string) => void
  className?: string
}

export function IELTSTestCard({
  test,
  userRole = 'STUDENT',
  onTakeTest,
  onViewResults,
  onViewDetails,
  onEditTest,
  className
}: IELTSTestCardProps) {
  const skillIcons = {
    LISTENING: Headphones,
    READING: BookOpen,
    WRITING: PenTool,
    SPEAKING: Mic
  }

  const getIELTSTypeColor = (type: IELTSType) => {
    return type === IELTSType.ACADEMIC 
      ? 'bg-blue-100 text-blue-800' 
      : 'bg-green-100 text-green-800'
  }

  const getSkillsBreakdown = () => {
    if (!test.sections) return null
    
    const skillCounts = test.sections.reduce((acc, section) => {
      const skill = section.skill as keyof typeof skillIcons
      if (skill && skillIcons[skill]) {
        acc[skill] = (acc[skill] || 0) + section._count.questions
      }
      return acc
    }, {} as Record<string, number>)

    return Object.entries(skillCounts).map(([skill, count]) => {
      const Icon = skillIcons[skill as keyof typeof skillIcons]
      return (
        <div key={skill} className="flex items-center space-x-1 text-xs text-gray-600">
          <Icon className="h-3 w-3" />
          <span>{count}</span>
        </div>
      )
    })
  }

  return (
    <Card className={cn("hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg mb-2">{test.title}</CardTitle>
            <div className="flex flex-wrap items-center gap-2 mb-2">
              <Badge className={getIELTSTypeColor(test.ieltsType)}>
                IELTS {test.ieltsType.replace('_', ' ')}
              </Badge>
              {test.bandScoring && (
                <Badge variant="outline">Band Scoring</Badge>
              )}
              {test.isPublic && (
                <Badge variant="secondary">Public</Badge>
              )}
            </div>
          </div>
          
          <div className="text-right text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <Users className="h-4 w-4" />
              <span>{test._count.results}</span>
            </div>
          </div>
        </div>
        
        {test.description && (
          <p className="text-sm text-gray-600 line-clamp-2">
            {test.description}
          </p>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Test Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span>{formatTime(test.duration)}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <BookOpen className="h-4 w-4 text-gray-400" />
            <span>{test._count.sections} sections</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span>{formatDateTime(test.createdAt)}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-gray-400" />
            <span>{test.creator.name}</span>
          </div>
        </div>

        {/* Skills Breakdown */}
        {test.sections && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Skills Coverage</h4>
            <div className="flex flex-wrap gap-3">
              {getSkillsBreakdown()}
            </div>
          </div>
        )}

        {/* Sections Preview */}
        {test.sections && test.sections.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Sections</h4>
            <div className="space-y-1">
              {test.sections.slice(0, 3).map((section) => {
                const Icon = skillIcons[section.skill as keyof typeof skillIcons] || BookOpen
                return (
                  <div key={section.id} className="flex items-center justify-between text-xs bg-gray-50 p-2 rounded">
                    <div className="flex items-center space-x-2">
                      <Icon className="h-3 w-3 text-gray-500" />
                      <span className="font-medium">{section.title}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-500">
                      <span>{section._count.questions} questions</span>
                      {section.duration && (
                        <>
                          <span>•</span>
                          <span>{formatTime(section.duration)}</span>
                        </>
                      )}
                    </div>
                  </div>
                )
              })}
              {test.sections.length > 3 && (
                <div className="text-xs text-gray-500 text-center py-1">
                  +{test.sections.length - 3} more sections
                </div>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-wrap gap-2 pt-2 border-t">
          {userRole === 'STUDENT' && onTakeTest && (
            <Button 
              onClick={() => onTakeTest(test.id)}
              size="sm"
              className="flex-1"
            >
              <Play className="h-4 w-4 mr-1" />
              Take Test
            </Button>
          )}
          
          {onViewDetails && (
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onViewDetails(test.id)}
            >
              <Eye className="h-4 w-4 mr-1" />
              Details
            </Button>
          )}
          
          {(userRole === 'TEACHER' || userRole === 'ADMIN') && onViewResults && (
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onViewResults(test.id)}
            >
              <BarChart3 className="h-4 w-4 mr-1" />
              Results
            </Button>
          )}
          
          {(userRole === 'TEACHER' || userRole === 'ADMIN') && onEditTest && (
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onEditTest(test.id)}
            >
              Edit
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface IELTSTestGridProps {
  tests: IELTSTest[]
  userRole?: 'ADMIN' | 'TEACHER' | 'STUDENT'
  onTakeTest?: (testId: string) => void
  onViewResults?: (testId: string) => void
  onViewDetails?: (testId: string) => void
  onEditTest?: (testId: string) => void
  className?: string
}

export function IELTSTestGrid({
  tests,
  userRole,
  onTakeTest,
  onViewResults,
  onViewDetails,
  onEditTest,
  className
}: IELTSTestGridProps) {
  if (tests.length === 0) {
    return (
      <div className={cn("text-center py-12", className)}>
        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No IELTS Tests</h3>
        <p className="text-gray-500">No IELTS tests are available at the moment.</p>
      </div>
    )
  }

  return (
    <div className={cn(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
      className
    )}>
      {tests.map((test) => (
        <IELTSTestCard
          key={test.id}
          test={test}
          userRole={userRole}
          onTakeTest={onTakeTest}
          onViewResults={onViewResults}
          onViewDetails={onViewDetails}
          onEditTest={onEditTest}
        />
      ))}
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, canAccessResource, handleApiError, validateRequiredFields } from '@/lib/middleware'
import { db } from '@/lib/db'
import { hashPassword } from '@/lib/auth'
import { UserRole } from '@prisma/client'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const userId = params.id

    // Get the target user
    const targetUser = await db.user.findUnique({
      where: { id: userId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        profile: true,
        _count: {
          select: {
            createdTests: true,
            testResults: true,
            classStudents: true,
            teacherClasses: true
          }
        }
      }
    })

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check permissions
    const canAccess = canAccessResource(
      user.role,
      user.id,
      targetUser.id,
      targetUser.organizationId,
      user.organizationId
    )

    if (!canAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Remove password from response
    const { password: _, ...userResponse } = targetUser

    return NextResponse.json({ user: userResponse })
  } catch (error) {
    return handleApiError(error, 'GET /api/users/[id]')
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const userId = params.id
    const body = await request.json()

    // Get the target user
    const targetUser = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        organizationId: true,
        role: true
      }
    })

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check permissions
    const canEdit = canAccessResource(
      user.role,
      user.id,
      targetUser.id,
      targetUser.organizationId,
      user.organizationId
    )

    if (!canEdit) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const {
      name,
      email,
      password,
      role,
      organizationId,
      profile
    } = body

    // Validate email format if provided
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return NextResponse.json(
          { error: 'Invalid email format' },
          { status: 400 }
        )
      }

      // Check if email is already taken by another user
      const existingUser = await db.user.findFirst({
        where: {
          email,
          id: { not: userId }
        }
      })

      if (existingUser) {
        return NextResponse.json(
          { error: 'Email already taken' },
          { status: 409 }
        )
      }
    }

    // Only admins can change roles and organization
    if ((role || organizationId) && user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Only admins can change role or organization' },
        { status: 403 }
      )
    }

    // Validate role if provided
    if (role && !Object.values(UserRole).includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      )
    }

    // Validate organization if provided
    if (organizationId) {
      const organization = await db.organization.findUnique({
        where: { id: organizationId }
      })

      if (!organization) {
        return NextResponse.json(
          { error: 'Organization not found' },
          { status: 400 }
        )
      }
    }

    // Prepare update data
    const updateData: any = {}
    
    if (name) updateData.name = name
    if (email) updateData.email = email
    if (role) updateData.role = role
    if (organizationId !== undefined) updateData.organizationId = organizationId

    // Hash password if provided
    if (password) {
      updateData.password = await hashPassword(password)
    }

    // Update user
    const updatedUser = await db.user.update({
      where: { id: userId },
      data: updateData,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        profile: true
      }
    })

    // Update profile if provided
    if (profile) {
      const profileData = {
        firstName: profile.firstName,
        lastName: profile.lastName,
        dateOfBirth: profile.dateOfBirth ? new Date(profile.dateOfBirth) : undefined,
        phoneNumber: profile.phoneNumber,
        address: profile.address,
        city: profile.city,
        country: profile.country,
        currentLevel: profile.currentLevel,
        targetLevel: profile.targetLevel,
        ieltsTarget: profile.ieltsTarget,
        previousIELTS: profile.previousIELTS,
        ieltsTestDate: profile.ieltsTestDate ? new Date(profile.ieltsTestDate) : undefined,
        preferredLanguage: profile.preferredLanguage,
        timezone: profile.timezone,
        notifications: profile.notifications
      }

      // Remove undefined values
      Object.keys(profileData).forEach(key => {
        if (profileData[key] === undefined) {
          delete profileData[key]
        }
      })

      if (Object.keys(profileData).length > 0) {
        await db.userProfile.upsert({
          where: { userId },
          update: profileData,
          create: {
            userId,
            ...profileData
          }
        })
      }
    }

    // Get updated user with profile
    const finalUser = await db.user.findUnique({
      where: { id: userId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        profile: true
      }
    })

    // Remove password from response
    const { password: _, ...userResponse } = finalUser!

    return NextResponse.json({ user: userResponse })
  } catch (error) {
    return handleApiError(error, 'PUT /api/users/[id]')
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const userId = params.id

    // Only admins can delete users
    if (user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Prevent self-deletion
    if (user.id === userId) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      )
    }

    // Check if user exists
    const targetUser = await db.user.findUnique({
      where: { id: userId },
      include: {
        _count: {
          select: {
            createdTests: true,
            testResults: true,
            classStudents: true,
            teacherClasses: true
          }
        }
      }
    })

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check if user has associated data
    const hasData = 
      targetUser._count.createdTests > 0 ||
      targetUser._count.testResults > 0 ||
      targetUser._count.classStudents > 0 ||
      targetUser._count.teacherClasses > 0

    if (hasData) {
      return NextResponse.json(
        { error: 'Cannot delete user with associated data. Consider deactivating instead.' },
        { status: 400 }
      )
    }

    // Delete user (profile will be deleted due to cascade)
    await db.user.delete({
      where: { id: userId }
    })

    return NextResponse.json({ message: 'User deleted successfully' })
  } catch (error) {
    return handleApiError(error, 'DELETE /api/users/[id]')
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, handleApiError, validateRequiredFields } from '@/lib/middleware'
import { db } from '@/lib/db'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const classId = params.id
    const body = await request.json()
    
    const requiredFields = ['testId']
    const missingFields = validateRequiredFields(body, requiredFields)

    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      )
    }

    const {
      testId,
      dueDate,
      startDate,
      endDate,
      allowLateSubmission,
      maxAttempts
    } = body

    // Check if user can manage this class
    const classData = await db.class.findUnique({
      where: { id: classId },
      select: {
        id: true,
        name: true,
        teacherId: true,
        organizationId: true
      }
    })

    if (!classData) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    const canManage = 
      user.role === 'ADMIN' ||
      classData.teacherId === user.id

    if (!canManage) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if test exists and user has access to it
    const test = await db.test.findUnique({
      where: { id: testId },
      select: {
        id: true,
        title: true,
        creatorId: true,
        organizationId: true,
        isPublic: true
      }
    })

    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 })
    }

    // Check if user can assign this test
    const canAssignTest = 
      user.role === 'ADMIN' ||
      test.creatorId === user.id ||
      test.isPublic ||
      (test.organizationId && test.organizationId === user.organizationId)

    if (!canAssignTest) {
      return NextResponse.json(
        { error: 'You do not have permission to assign this test' },
        { status: 403 }
      )
    }

    // Validate dates
    const now = new Date()
    const parsedStartDate = startDate ? new Date(startDate) : null
    const parsedEndDate = endDate ? new Date(endDate) : null
    const parsedDueDate = dueDate ? new Date(dueDate) : null

    if (parsedStartDate && parsedEndDate && parsedEndDate <= parsedStartDate) {
      return NextResponse.json(
        { error: 'End date must be after start date' },
        { status: 400 }
      )
    }

    if (parsedDueDate && parsedDueDate <= now) {
      return NextResponse.json(
        { error: 'Due date must be in the future' },
        { status: 400 }
      )
    }

    // Check if test is already assigned to this class
    const existingAssignment = await db.testAssignment.findFirst({
      where: {
        testId,
        classId
      }
    })

    if (existingAssignment) {
      return NextResponse.json(
        { error: 'Test is already assigned to this class' },
        { status: 409 }
      )
    }

    // Create test assignment
    const assignment = await db.testAssignment.create({
      data: {
        testId,
        classId,
        dueDate: parsedDueDate,
        startDate: parsedStartDate,
        endDate: parsedEndDate,
        allowLateSubmission: allowLateSubmission || false,
        maxAttempts: maxAttempts || 1
      },
      include: {
        test: {
          select: {
            id: true,
            title: true,
            type: true,
            duration: true,
            level: true
          }
        },
        class: {
          select: {
            id: true,
            name: true,
            _count: {
              select: {
                students: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({ 
      assignment,
      message: `Test "${test.title}" assigned to class "${classData.name}" successfully`
    }, { status: 201 })
  } catch (error) {
    return handleApiError(error, 'POST /api/classes/[id]/assign-test')
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const classId = params.id

    // Check if user can view this class
    const classData = await db.class.findUnique({
      where: { id: classId },
      select: {
        teacherId: true,
        students: {
          where: { studentId: user.id },
          select: { studentId: true }
        }
      }
    })

    if (!classData) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    const canView = 
      user.role === 'ADMIN' ||
      classData.teacherId === user.id ||
      classData.students.length > 0 // User is a student in this class

    if (!canView) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get all test assignments for this class
    const assignments = await db.testAssignment.findMany({
      where: { classId },
      include: {
        test: {
          select: {
            id: true,
            title: true,
            description: true,
            type: true,
            level: true,
            duration: true,
            passingScore: true,
            maxAttempts: true,
            _count: {
              select: {
                results: {
                  where: {
                    user: {
                      classStudents: {
                        some: {
                          classId,
                          status: 'ACTIVE'
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      orderBy: {
        assignedAt: 'desc'
      }
    })

    // If user is a student, also include their results for each test
    let assignmentsWithResults = assignments
    if (user.role === 'STUDENT') {
      assignmentsWithResults = await Promise.all(
        assignments.map(async (assignment) => {
          const userResults = await db.testResult.findMany({
            where: {
              testId: assignment.testId,
              userId: user.id
            },
            select: {
              id: true,
              attempt: true,
              score: true,
              percentage: true,
              passed: true,
              submittedAt: true,
              status: true
            },
            orderBy: {
              attempt: 'desc'
            }
          })

          return {
            ...assignment,
            userResults
          }
        })
      )
    }

    return NextResponse.json({ assignments: assignmentsWithResults })
  } catch (error) {
    return handleApiError(error, 'GET /api/classes/[id]/assign-test')
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, error } = await requireAuth(request)
    if (error) return error

    const classId = params.id
    const { searchParams } = new URL(request.url)
    const assignmentId = searchParams.get('assignmentId')

    if (!assignmentId) {
      return NextResponse.json(
        { error: 'Assignment ID is required' },
        { status: 400 }
      )
    }

    // Check if user can manage this class
    const classData = await db.class.findUnique({
      where: { id: classId },
      select: {
        teacherId: true
      }
    })

    if (!classData) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    const canManage = 
      user.role === 'ADMIN' ||
      classData.teacherId === user.id

    if (!canManage) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if assignment exists and belongs to this class
    const assignment = await db.testAssignment.findFirst({
      where: {
        id: assignmentId,
        classId
      },
      include: {
        test: {
          select: {
            title: true,
            _count: {
              select: {
                results: {
                  where: {
                    user: {
                      classStudents: {
                        some: {
                          classId,
                          status: 'ACTIVE'
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!assignment) {
      return NextResponse.json(
        { error: 'Assignment not found' },
        { status: 404 }
      )
    }

    // Check if any students have already taken the test
    if (assignment.test._count.results > 0) {
      return NextResponse.json(
        { error: 'Cannot remove assignment - students have already taken this test' },
        { status: 400 }
      )
    }

    // Delete the assignment
    await db.testAssignment.delete({
      where: { id: assignmentId }
    })

    return NextResponse.json({
      message: `Test assignment removed successfully`
    })
  } catch (error) {
    return handleApiError(error, 'DELETE /api/classes/[id]/assign-test')
  }
}

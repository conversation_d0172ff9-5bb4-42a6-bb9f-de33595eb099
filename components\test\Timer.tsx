"use client"

import { useState, useEffect, useCallback } from "react"
import { Clock, Pause, Play } from "lucide-react"
import { cn } from "@/lib/utils"
import { formatTime } from "@/lib/utils"

interface TimerProps {
  duration: number // Duration in seconds
  onTimeUp?: () => void
  onTimeUpdate?: (timeRemaining: number) => void
  autoStart?: boolean
  showControls?: boolean
  className?: string
}

export function Timer({ 
  duration, 
  onTimeUp, 
  onTimeUpdate,
  autoStart = true,
  showControls = false,
  className 
}: TimerProps) {
  const [timeRemaining, setTimeRemaining] = useState(duration)
  const [isRunning, setIsRunning] = useState(autoStart)
  const [isPaused, setIsPaused] = useState(false)

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isRunning && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          const newTime = prev - 1
          onTimeUpdate?.(newTime)
          
          if (newTime <= 0) {
            setIsRunning(false)
            onTimeUp?.()
            return 0
          }
          
          return newTime
        })
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [isRunning, timeRemaining, onTimeUp, onTimeUpdate])

  const handlePause = useCallback(() => {
    setIsRunning(false)
    setIsPaused(true)
  }, [])

  const handleResume = useCallback(() => {
    setIsRunning(true)
    setIsPaused(false)
  }, [])

  const handleReset = useCallback(() => {
    setTimeRemaining(duration)
    setIsRunning(autoStart)
    setIsPaused(false)
  }, [duration, autoStart])

  const percentage = ((duration - timeRemaining) / duration) * 100
  const isWarning = timeRemaining <= 300 // Last 5 minutes
  const isCritical = timeRemaining <= 60 // Last minute

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <div className="flex items-center space-x-2">
        <Clock className={cn(
          "h-5 w-5",
          isCritical ? "text-red-500" : isWarning ? "text-yellow-500" : "text-gray-500"
        )} />
        
        <div className="text-lg font-mono font-semibold">
          <span className={cn(
            isCritical ? "text-red-500" : isWarning ? "text-yellow-500" : "text-gray-900"
          )}>
            {formatTime(timeRemaining)}
          </span>
        </div>
      </div>

      {/* Progress bar */}
      <div className="flex-1 bg-gray-200 rounded-full h-2 max-w-32">
        <div 
          className={cn(
            "h-2 rounded-full transition-all duration-1000",
            isCritical ? "bg-red-500" : isWarning ? "bg-yellow-500" : "bg-blue-500"
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>

      {/* Controls */}
      {showControls && (
        <div className="flex items-center space-x-1">
          {isRunning ? (
            <button
              onClick={handlePause}
              className="p-1 rounded hover:bg-gray-100 transition-colors"
              title="Pause timer"
            >
              <Pause className="h-4 w-4" />
            </button>
          ) : (
            <button
              onClick={handleResume}
              className="p-1 rounded hover:bg-gray-100 transition-colors"
              title="Resume timer"
            >
              <Play className="h-4 w-4" />
            </button>
          )}
          
          <button
            onClick={handleReset}
            className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            title="Reset timer"
          >
            Reset
          </button>
        </div>
      )}
    </div>
  )
}

interface CountUpTimerProps {
  onTimeUpdate?: (timeElapsed: number) => void
  autoStart?: boolean
  className?: string
}

export function CountUpTimer({ 
  onTimeUpdate, 
  autoStart = true, 
  className 
}: CountUpTimerProps) {
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [isRunning, setIsRunning] = useState(autoStart)

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isRunning) {
      interval = setInterval(() => {
        setTimeElapsed(prev => {
          const newTime = prev + 1
          onTimeUpdate?.(newTime)
          return newTime
        })
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [isRunning, onTimeUpdate])

  const handleToggle = () => {
    setIsRunning(!isRunning)
  }

  const handleReset = () => {
    setTimeElapsed(0)
    setIsRunning(autoStart)
  }

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <div className="flex items-center space-x-2">
        <Clock className="h-5 w-5 text-gray-500" />
        <div className="text-lg font-mono font-semibold">
          {formatTime(timeElapsed)}
        </div>
      </div>

      <div className="flex items-center space-x-1">
        <button
          onClick={handleToggle}
          className="p-1 rounded hover:bg-gray-100 transition-colors"
          title={isRunning ? "Pause timer" : "Start timer"}
        >
          {isRunning ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
        </button>
        
        <button
          onClick={handleReset}
          className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
          title="Reset timer"
        >
          Reset
        </button>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { CheckCircle, XCircle } from 'lucide-react'

interface TrueFalseBuilderProps {
  correctAnswer: any
  onCorrectAnswerChange: (correctAnswer: any) => void
  error?: string
}

export function TrueFalseBuilder({
  correctAnswer,
  onCorrectAnswerChange,
  error
}: TrueFalseBuilderProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<boolean | null>(null)

  useEffect(() => {
    if (correctAnswer?.answer !== undefined) {
      setSelectedAnswer(correctAnswer.answer)
    }
  }, [correctAnswer])

  useEffect(() => {
    if (selectedAnswer !== null) {
      onCorrectAnswerChange({
        answer: selectedAnswer,
        correct: selectedAnswer
      })
    }
  }, [selectedAnswer, onCorrectAnswerChange])

  return (
    <Card>
      <CardHeader>
        <CardTitle>True/False Answer</CardTitle>
        <CardDescription>
          Select the correct answer for this true/false question
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        <div className="space-y-3">
          <Label>Correct Answer</Label>
          <RadioGroup
            value={selectedAnswer?.toString() || ''}
            onValueChange={(value) => setSelectedAnswer(value === 'true')}
          >
            <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="true" id="true" />
              <div className="flex items-center gap-2 flex-1">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <Label htmlFor="true" className="text-base font-medium cursor-pointer">
                  True
                </Label>
              </div>
            </div>

            <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <RadioGroupItem value="false" id="false" />
              <div className="flex items-center gap-2 flex-1">
                <XCircle className="h-5 w-5 text-red-600" />
                <Label htmlFor="false" className="text-base font-medium cursor-pointer">
                  False
                </Label>
              </div>
            </div>
          </RadioGroup>
        </div>

        {/* Selected Answer Indicator */}
        {selectedAnswer !== null && (
          <div className={`p-3 border rounded-lg ${
            selectedAnswer 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className={`text-sm font-medium flex items-center gap-2 ${
              selectedAnswer ? 'text-green-800' : 'text-red-800'
            }`}>
              {selectedAnswer ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <XCircle className="h-4 w-4" />
              )}
              Correct Answer: {selectedAnswer ? 'True' : 'False'}
            </div>
          </div>
        )}

        {/* Preview */}
        <div className="pt-4 border-t">
          <Label className="text-sm font-medium">Preview</Label>
          <div className="mt-2 p-4 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium mb-3">Student will see:</p>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
                <span className="text-sm">True</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
                <span className="text-sm">False</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First check if the test exists and user has access
    const test = await db.test.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: { id: true }
        }
      }
    })

    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 })
    }

    // Check access permissions
    const canAccess = 
      test.isPublic ||
      session.user.role === 'ADMIN' ||
      test.creator.id === session.user.id ||
      (session.user.role === 'STUDENT' && await checkStudentAccess(test.id, session.user.id))

    if (!canAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const sections = await db.testSection.findMany({
      where: { testId: params.id },
      include: {
        questions: {
          orderBy: { order: 'asc' }
        },
        _count: {
          select: {
            questions: true
          }
        }
      },
      orderBy: { order: 'asc' }
    })

    return NextResponse.json({ sections })
  } catch (error) {
    console.error('Failed to fetch test sections:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers and admins can create sections
    if (session.user.role === 'STUDENT') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const test = await db.test.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: { id: true }
        }
      }
    })

    if (!test) {
      return NextResponse.json({ error: 'Test not found' }, { status: 404 })
    }

    // Check edit permissions
    const canEdit = 
      session.user.role === 'ADMIN' ||
      test.creator.id === session.user.id

    if (!canEdit) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const body = await request.json()
    const { title, description, duration, skill } = body

    // Validation
    if (!title) {
      return NextResponse.json(
        { error: 'Section title is required' },
        { status: 400 }
      )
    }

    // Get the next order number
    const lastSection = await db.testSection.findFirst({
      where: { testId: params.id },
      orderBy: { order: 'desc' }
    })
    const nextOrder = (lastSection?.order || 0) + 1

    const section = await db.testSection.create({
      data: {
        testId: params.id,
        title,
        description,
        order: nextOrder,
        duration,
        skill
      },
      include: {
        _count: {
          select: {
            questions: true
          }
        }
      }
    })

    return NextResponse.json({ section }, { status: 201 })
  } catch (error) {
    console.error('Failed to create test section:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to check if a student has access to a test
async function checkStudentAccess(testId: string, studentId: string): Promise<boolean> {
  const assignment = await db.testAssignment.findFirst({
    where: {
      testId,
      class: {
        students: {
          some: {
            studentId
          }
        }
      }
    }
  })

  return !!assignment
}
